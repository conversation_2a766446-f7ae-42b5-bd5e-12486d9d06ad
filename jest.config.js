const env = require("@dfapp/configs/context/env");
process.env.__JEST__ = "true";

module.exports = {
    preset: "react-native",
    moduleDirectories: ["node_modules", "<rootDir>/src"],
    moduleFileExtensions: env.scriptExts,
    moduleNameMapper: {
        "\\.svg": "<rootDir>/jest/__mocks__/svgMock.js",
        "^@/client/(.*)$": "<rootDir>/src/$1",
        "^@/shared/(.*)$": "<rootDir>/packages/shared/src/$1",
    },
    transformIgnorePatterns: [
        `node_modules/(?!((jest-)?react-native|${[
            "@invertase/react-native-apple-authentication",
            "@react-native(-community)?",
            "@react-native-firebase",
            "@react-navigation",
            "jail-monkey",
            "react-clone-referenced-element",
            "react-native-geolocation-service",
            "react-native-iphone-x-helper",
            "react-native-linear-gradient",
            "react-native-markdown-renderer",
            "react-native-paper",
            "react-native-permissions",
            "react-native-reanimated",
            "react-native-vector-icons",
            "rn-material-ui-textfield",
            "rn-placeholder",
        ].join("|")})/)`,
    ],
    testPathIgnorePatterns: ["/node_modules/"],
    coverageDirectory: "<rootDir>/tmp/coverage",
    coverageReporters: ["lcov", "text-summary"],
    collectCoverageFrom: [
        "**/*.{ts,tsx}",
        "<rootDir>/src/**",
        "!<rootDir>/src/types.ts",
        "!**/*.json",
        "!**/*.d.ts",
        "!**/__tests__/**",
        "!**/node_modules/**",
    ],
    setupFiles: ["<rootDir>/jest/setup.js"],
    globals: {
        __DEV__: false,
    },
};
