{"name": "dfapp", "version": "0.0.0", "main": "src/index.tsx", "repository": "bitbucket:flirtenteam/df-app-rn", "license": "UNLICENSED", "private": true, "engineStrict": true, "engines": {"node": "18 || 20", "yarn": "^1.12.0"}, "scripts": {"test": "BUNDLE_APP=popcorn scripts/test.sh", "setup": "scripts/install/install-for-develop.sh", "start": "BUNDLE_APP=popcorn react-native start --port 8081", "start:fet": "BUNDLE_APP=fet react-native start --port 8082", "start:gaudi": "BUNDLE_APP=gaudi react-native start --port 8084", "lint-img": "node scripts/lint/lint-image.js", "lint-src": "eslint --ext .js,.jsx,.ts,.tsx src", "lint-tsc": "tsc -p .", "bump": "node scripts/bump.js", "bitrise": "node scripts/bitrise.js", "upload": "scripts/upload/upload-release.sh", "disclaimers": "yarn licenses generate-disclaimer --prod > static/DISCLAIMER.txt", "gradle": "android/gradlew --no-daemon -p android", "pod": "pod --project-directory=ios", "pods": "pod install --project-directory=ios --silent", "i18n": "node scripts/list-i18n.js"}, "workspaces": ["packages/_configs", "packages/_typings", "packages/shared", "scripts"], "dependencies": {"@amplitude/analytics-react-native": "1.4.0", "@invertase/react-native-apple-authentication": "2.4.1", "@ptomasroos/react-native-multi-slider": "2.2.2", "@react-native-async-storage/async-storage": "2.1.2", "@react-native-clipboard/clipboard": "1.16.2", "@react-native-community/blur": "4.4.1", "@react-native-community/datetimepicker": "8.3.0", "@react-native-community/netinfo": "11.4.1", "@react-native-firebase/analytics": "21.11.0", "@react-native-firebase/app": "21.11.0", "@react-native-firebase/crashlytics": "21.11.0", "@react-native-firebase/in-app-messaging": "21.11.0", "@react-native-firebase/messaging": "21.11.0", "@react-native-firebase/remote-config": "21.11.0", "@react-native-google-signin/google-signin": "13.2.0", "@react-navigation/bottom-tabs": "5.11.15", "@react-navigation/core": "5.16.1", "@react-navigation/devtools": "5.1.26", "@react-navigation/material-top-tabs": "5.3.19", "@react-navigation/native": "5.9.8", "@react-navigation/routers": "5.7.4", "@react-navigation/stack": "5.14.9", "@sayem314/react-native-keep-awake": "1.3.1", "@sentry/react-native": "6.13.1", "axios": "0.27.2", "color": "3.2.1", "component-emitter": "1.3.0", "crypto-js": "3.3.0", "date-fns": "1.30.1", "deep-diff": "1.0.2", "emoji-flag": "1.1.0", "engine.io-client": "3.4.0", "events": "3.3.0", "jail-monkey": "2.8.3", "lodash": "4.17.21", "lru-cache": "6.0.0", "markdown-it": "13.0.1", "markdown-it-emoji": "2.0.2", "mqtt": "4.3.7", "path-to-regexp": "1.7.0", "posthog-react-native": "3.15.1", "query-string": "6.13.1", "react": "18.2.0", "react-native": "0.73.8", "react-native-adapty": "3.3.1", "react-native-adjust": "5.1.0", "react-native-agora": "4.5.2", "react-native-animatable": "1.4.0", "react-native-applovin-max": "9.1.0", "react-native-awesome-gallery": "0.4.3", "react-native-card-stack-swiper": "1.2.5", "react-native-config": "0.11.7", "react-native-controlled-mentions": "2.2.5", "react-native-create-thumbnail": "2.1.1", "react-native-device-info": "11.1.0", "react-native-geolocation-service": "5.3.1", "react-native-gesture-handler": "2.15.0", "react-native-haptic-feedback": "2.3.3", "react-native-image-crop-picker": "0.42.0", "react-native-linear-gradient": "2.8.3", "react-native-localize": "3.4.1", "react-native-maps": "1.11.3", "react-native-markdown-renderer": "3.2.8", "react-native-pager-view": "5.4.25", "react-native-paper": "5.14.3", "react-native-permissions": "5.2.6", "react-native-progress": "5.0.1", "react-native-reanimated": "3.16.7", "react-native-reanimated-carousel": "4.0.2", "react-native-safe-area-context": "4.9.0", "react-native-safe-area-view": "1.1.1", "react-native-screens": "3.29.0", "react-native-share": "10.0.2", "react-native-svg": "15.11.2", "react-native-tab-view": "3.1.1", "react-native-touch-id": "4.4.1", "react-native-vector-icons": "10.0.0", "react-native-video": "6.10.1", "react-native-vision-camera": "4.6.1", "react-native-webview": "13.13.5", "react-redux": "8.1.2", "realm": "20.1.0", "redux": "4.2.1", "redux-persist": "6.0.0", "reselect": "4.1.8", "rn-material-ui-textfield": "1.0.9", "rn-placeholder": "3.0.3", "semver": "7.5.4", "socket.io-parser": "3.4.0", "sprintf-js": "1.1.2", "uri-js": "4.2.0", "util": "0.12.4", "uuid": "3.3.3"}, "devDependencies": {"@babel/core": "^7.25.0", "@babel/preset-typescript": "^7.25.0", "@react-native/babel-preset": "0.73.21", "@react-native/eslint-config": "0.73.2", "@react-native/metro-config": "0.73.5", "@react-native/typescript-config": "0.73.1", "@testing-library/react-hooks": "^8.0.1", "@testing-library/react-native": "^13.2.0", "@types/color": "4.2.0", "@types/crypto-js": "3.1.47", "@types/engine.io-client": "3.1.4", "@types/jest": "29.5.12", "@types/lodash": "4.14.197", "@types/lru-cache": "5.1.1", "@types/markdown-it": "13.0.1", "@types/ptomasroos__react-native-multi-slider": "2.2.1", "@types/react": "18.2.22", "@types/react-native-vector-icons": "6.4.11", "@types/react-test-renderer": "18.0.2", "@types/redux-mock-store": "1.0.3", "@types/rn-material-ui-textfield": "1.0.2", "@types/semver": "7.5.1", "@types/socket.io-parser": "2.2.1", "@types/sprintf-js": "1.1.2", "@types/use-subscription": "1.0.0", "@types/uuid": "3.4.6", "@typescript-eslint/eslint-plugin": "6.5.0", "@typescript-eslint/parser": "6.5.0", "babel-jest": "29.7.0", "babel-plugin-module-resolver": "5.0.2", "babel-plugin-transform-define": "2.1.4", "eslint": "8.47.0", "eslint-plugin-react": "7.33.2", "jest": "29.7.0", "patch-package": "8.0.0", "react-native-svg-transformer": "1.5.0", "react-test-renderer": "18.2.0", "redux-mock-store": "1.5.4", "typescript": "5.0.4", "xhr-mock": "2.5.1"}, "resolutions": {"@types/markdown-it": "13.0.1", "@types/node": "18.19.17", "@types/react": "18.2.22", "@types/react-native": "0.73.0", "@react-native-async-storage/async-storage": "2.1.2", "ansi-styles": "4.3.0", "component-emitter": "1.3.0", "debug": "4.3.4", "define-property": "2.0.2", "extend-shallow": "3.0.2", "fsevents": "2.3.3", "hoist-non-react-statics": "3.3.2", "iconv-lite": "0.6.3", "inherits": "2.0.4", "is-extendable": "1.0.1", "isarray": "2.0.5", "isobject": "4.0.0", "json5": "2.2.3", "kind-of": "6.0.3", "markdown-it": "13.0.1", "punycode": "2.1.1", "react-is": "18.2.0", "react-native": "0.73.8", "rimraf": "3.0.2", "safe-buffer": "5.2.1", "supports-color": "8.1.1", "whatwg-fetch": "3.6.20"}, "optionalDependencies": {"fsevents": "2.3.3"}}