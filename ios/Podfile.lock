PODS:
  - Adapty (3.3.4)
  - AdaptyPlugin (3.3.4):
    - Adapty (= 3.3.4)
    - AdaptyUI (= 3.3.4)
  - AdaptyUI (3.3.4):
    - Adapty (= 3.3.4)
  - Adjust (5.1.1):
    - Adjust/Adjust (= 5.1.1)
  - Adjust/Adjust (5.1.1):
    - AdjustSignature (= 3.35.2)
  - AdjustSignature (3.35.2)
  - AgoraInfra_iOS (1.2.13)
  - AgoraIrisRTC_iOS (4.5.1-build.1)
  - AgoraRtcEngine_iOS/AIAEC (4.5.1)
  - AgoraRtcEngine_iOS/AINS (4.5.1)
  - AgoraRtcEngine_iOS/RtcBasic (4.5.1):
    - AgoraInfra_iOS (= 1.2.13)
  - AgoraRtcEngine_iOS/VideoCodecDec (4.5.1)
  - AgoraRtcEngine_iOS/VideoCodecEnc (4.5.1)
  - AgoraRtcEngine_iOS/VQA (4.5.1)
  - amplitude-react-native (1.4.0):
    - React-Core
  - AppLovinMediationFacebookAdapter (6.17.1.0):
    - AppLovinSDK (>= 13.0.0)
    - FBAudienceNetwork (= 6.17.1)
  - AppLovinMediationFyberAdapter (8.3.6.0):
    - AppLovinSDK (>= 13.0.0)
    - Fyber_Marketplace_SDK (= 8.3.6)
  - AppLovinMediationGoogleAdapter (12.4.0.1):
    - AppLovinSDK (>= 13.0.0)
    - Google-Mobile-Ads-SDK (= 12.4.0)
  - AppLovinMediationGoogleAdManagerAdapter (12.4.0.1):
    - AppLovinSDK (>= 13.0.0)
    - Google-Mobile-Ads-SDK (= 12.4.0)
  - AppLovinMediationInMobiAdapter (10.8.3.1):
    - AppLovinSDK (>= 13.0.0)
    - InMobiSDK (= 10.8.3)
  - AppLovinMediationMintegralAdapter (7.7.8.0.0):
    - AppLovinSDK (>= 13.0.0)
    - MintegralAdSDK (= 7.7.8)
    - MintegralAdSDK/BidSplashAd (= 7.7.8)
  - AppLovinMediationMolocoAdapter (3.9.1.1):
    - AppLovinSDK (>= 13.0.0)
    - MolocoSDKiOS (= 3.9.1)
  - AppLovinMediationUnityAdsAdapter (4.14.2.0):
    - AppLovinSDK (>= 13.0.0)
    - UnityAds (= 4.14.2)
  - AppLovinMediationVerveAdapter (3.6.0.0):
    - AppLovinSDK (>= 13.0.0)
    - HyBid (= 3.6.0)
  - AppLovinMediationVungleAdapter (7.5.1.2):
    - AppLovinSDK (>= 13.0.0)
    - VungleAds (= 7.5.1)
  - AppLovinSDK (13.2.0)
  - ATOM-Standalone (3.6.0)
  - boost (1.83.0)
  - BVLinearGradient (2.8.3):
    - React-Core
  - DoubleConversion (1.1.6)
  - FBAudienceNetwork (6.17.1)
  - FBLazyVector (0.73.8)
  - FBReactNativeSpec (0.73.8):
    - RCT-Folly (= 2022.05.16.00)
    - RCTRequired (= 0.73.8)
    - RCTTypeSafety (= 0.73.8)
    - React-Core (= 0.73.8)
    - React-jsi (= 0.73.8)
    - ReactCommon/turbomodule/core (= 0.73.8)
  - Firebase/Analytics (11.8.0):
    - Firebase/Core
  - Firebase/Core (11.8.0):
    - Firebase/CoreOnly
    - FirebaseAnalytics (~> 11.8.0)
  - Firebase/CoreOnly (11.8.0):
    - FirebaseCore (~> 11.8.0)
  - Firebase/Crashlytics (11.8.0):
    - Firebase/CoreOnly
    - FirebaseCrashlytics (~> 11.8.0)
  - Firebase/InAppMessaging (11.8.0):
    - Firebase/CoreOnly
    - FirebaseInAppMessaging (~> 11.8.0-beta)
  - Firebase/Messaging (11.8.0):
    - Firebase/CoreOnly
    - FirebaseMessaging (~> 11.8.0)
  - Firebase/RemoteConfig (11.8.0):
    - Firebase/CoreOnly
    - FirebaseRemoteConfig (~> 11.8.0)
  - FirebaseABTesting (11.8.0):
    - FirebaseCore (~> 11.8.0)
  - FirebaseAnalytics (11.8.0):
    - FirebaseAnalytics/AdIdSupport (= 11.8.0)
    - FirebaseCore (~> 11.8.0)
    - FirebaseInstallations (~> 11.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/MethodSwizzler (~> 8.0)
    - GoogleUtilities/Network (~> 8.0)
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
    - nanopb (~> 3.30910.0)
  - FirebaseAnalytics/AdIdSupport (11.8.0):
    - FirebaseCore (~> 11.8.0)
    - FirebaseInstallations (~> 11.0)
    - GoogleAppMeasurement (= 11.8.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/MethodSwizzler (~> 8.0)
    - GoogleUtilities/Network (~> 8.0)
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
    - nanopb (~> 3.30910.0)
  - FirebaseCore (11.8.1):
    - FirebaseCoreInternal (~> 11.8.0)
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/Logger (~> 8.0)
  - FirebaseCoreExtension (11.8.0):
    - FirebaseCore (~> 11.8.0)
  - FirebaseCoreInternal (11.8.0):
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
  - FirebaseCrashlytics (11.8.0):
    - FirebaseCore (~> 11.8.0)
    - FirebaseInstallations (~> 11.0)
    - FirebaseRemoteConfigInterop (~> 11.0)
    - FirebaseSessions (~> 11.0)
    - GoogleDataTransport (~> 10.0)
    - GoogleUtilities/Environment (~> 8.0)
    - nanopb (~> 3.30910.0)
    - PromisesObjC (~> 2.4)
  - FirebaseInAppMessaging (11.8.0-beta):
    - FirebaseABTesting (~> 11.0)
    - FirebaseCore (~> 11.8.0)
    - FirebaseInstallations (~> 11.0)
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/UserDefaults (~> 8.0)
    - nanopb (~> 3.30910.0)
  - FirebaseInstallations (11.8.0):
    - FirebaseCore (~> 11.8.0)
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/UserDefaults (~> 8.0)
    - PromisesObjC (~> 2.4)
  - FirebaseMessaging (11.8.0):
    - FirebaseCore (~> 11.8.0)
    - FirebaseInstallations (~> 11.0)
    - GoogleDataTransport (~> 10.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/Reachability (~> 8.0)
    - GoogleUtilities/UserDefaults (~> 8.0)
    - nanopb (~> 3.30910.0)
  - FirebaseRemoteConfig (11.8.0):
    - FirebaseABTesting (~> 11.0)
    - FirebaseCore (~> 11.8.0)
    - FirebaseInstallations (~> 11.0)
    - FirebaseRemoteConfigInterop (~> 11.0)
    - FirebaseSharedSwift (~> 11.0)
    - GoogleUtilities/Environment (~> 8.0)
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
  - FirebaseRemoteConfigInterop (11.8.0)
  - FirebaseSessions (11.8.0):
    - FirebaseCore (~> 11.8.0)
    - FirebaseCoreExtension (~> 11.8.0)
    - FirebaseInstallations (~> 11.0)
    - GoogleDataTransport (~> 10.0)
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/UserDefaults (~> 8.0)
    - nanopb (~> 3.30910.0)
    - PromisesSwift (~> 2.1)
  - FirebaseSharedSwift (11.8.0)
  - fmt (6.2.1)
  - Fyber_Marketplace_SDK (8.3.6)
  - glog (0.3.5)
  - Google-Mobile-Ads-SDK (12.4.0):
    - GoogleUserMessagingPlatform (>= 1.1)
  - GoogleAppMeasurement (11.8.0):
    - GoogleAppMeasurement/AdIdSupport (= 11.8.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/MethodSwizzler (~> 8.0)
    - GoogleUtilities/Network (~> 8.0)
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
    - nanopb (~> 3.30910.0)
  - GoogleAppMeasurement/AdIdSupport (11.8.0):
    - GoogleAppMeasurement/WithoutAdIdSupport (= 11.8.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/MethodSwizzler (~> 8.0)
    - GoogleUtilities/Network (~> 8.0)
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
    - nanopb (~> 3.30910.0)
  - GoogleAppMeasurement/WithoutAdIdSupport (11.8.0):
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/MethodSwizzler (~> 8.0)
    - GoogleUtilities/Network (~> 8.0)
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
    - nanopb (~> 3.30910.0)
  - GoogleDataTransport (10.1.0):
    - nanopb (~> 3.30910.0)
    - PromisesObjC (~> 2.4)
  - GoogleUserMessagingPlatform (3.0.0)
  - GoogleUtilities (8.0.2):
    - GoogleUtilities/AppDelegateSwizzler (= 8.0.2)
    - GoogleUtilities/Environment (= 8.0.2)
    - GoogleUtilities/Logger (= 8.0.2)
    - GoogleUtilities/MethodSwizzler (= 8.0.2)
    - GoogleUtilities/Network (= 8.0.2)
    - "GoogleUtilities/NSData+zlib (= 8.0.2)"
    - GoogleUtilities/Privacy (= 8.0.2)
    - GoogleUtilities/Reachability (= 8.0.2)
    - GoogleUtilities/SwizzlerTestHelpers (= 8.0.2)
    - GoogleUtilities/UserDefaults (= 8.0.2)
  - GoogleUtilities/AppDelegateSwizzler (8.0.2):
    - GoogleUtilities/Environment
    - GoogleUtilities/Logger
    - GoogleUtilities/Network
    - GoogleUtilities/Privacy
  - GoogleUtilities/Environment (8.0.2):
    - GoogleUtilities/Privacy
  - GoogleUtilities/Logger (8.0.2):
    - GoogleUtilities/Environment
    - GoogleUtilities/Privacy
  - GoogleUtilities/MethodSwizzler (8.0.2):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/Network (8.0.2):
    - GoogleUtilities/Logger
    - "GoogleUtilities/NSData+zlib"
    - GoogleUtilities/Privacy
    - GoogleUtilities/Reachability
  - "GoogleUtilities/NSData+zlib (8.0.2)":
    - GoogleUtilities/Privacy
  - GoogleUtilities/Privacy (8.0.2)
  - GoogleUtilities/Reachability (8.0.2):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/SwizzlerTestHelpers (8.0.2):
    - GoogleUtilities/MethodSwizzler
  - GoogleUtilities/UserDefaults (8.0.2):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - hermes-engine (0.73.8):
    - hermes-engine/Pre-built (= 0.73.8)
  - hermes-engine/Pre-built (0.73.8)
  - HyBid (3.6.0):
    - HyBid/ATOM (= 3.6.0)
    - HyBid/Banner (= 3.6.0)
    - HyBid/Core (= 3.6.0)
    - HyBid/FullScreen (= 3.6.0)
    - HyBid/Native (= 3.6.0)
    - HyBid/RewardedVideo (= 3.6.0)
  - HyBid/ATOM (3.6.0):
    - ATOM-Standalone (~> 3.6.0)
    - HyBid/Core
  - HyBid/Banner (3.6.0):
    - HyBid/Core
  - HyBid/Core (3.6.0)
  - HyBid/FullScreen (3.6.0):
    - HyBid/Core
  - HyBid/Native (3.6.0):
    - HyBid/Core
  - HyBid/RewardedVideo (3.6.0):
    - HyBid/Core
  - InMobiSDK (10.8.3)
  - jail-monkey (2.8.3):
    - React-Core
  - lame (1.1.0)
  - libevent (2.1.12)
  - MintegralAdSDK (7.7.8):
    - MintegralAdSDK/BannerAd (= 7.7.8)
    - MintegralAdSDK/BidBannerAd (= 7.7.8)
    - MintegralAdSDK/BidInterstitialVideoAd (= 7.7.8)
    - MintegralAdSDK/BidNativeAd (= 7.7.8)
    - MintegralAdSDK/BidNewInterstitialAd (= 7.7.8)
    - MintegralAdSDK/BidRewardVideoAd (= 7.7.8)
    - MintegralAdSDK/InterstitialVideoAd (= 7.7.8)
    - MintegralAdSDK/NativeAd (= 7.7.8)
    - MintegralAdSDK/NewInterstitialAd (= 7.7.8)
    - MintegralAdSDK/RewardVideoAd (= 7.7.8)
  - MintegralAdSDK/BannerAd (7.7.8):
    - MintegralAdSDK/NativeAd
  - MintegralAdSDK/BidBannerAd (7.7.8):
    - MintegralAdSDK/BannerAd
    - MintegralAdSDK/BidNativeAd
  - MintegralAdSDK/BidInterstitialVideoAd (7.7.8):
    - MintegralAdSDK/BidNativeAd
    - MintegralAdSDK/InterstitialVideoAd
  - MintegralAdSDK/BidNativeAd (7.7.8):
    - MintegralAdSDK/NativeAd
  - MintegralAdSDK/BidNewInterstitialAd (7.7.8):
    - MintegralAdSDK/BidNativeAd
    - MintegralAdSDK/NewInterstitialAd
  - MintegralAdSDK/BidRewardVideoAd (7.7.8):
    - MintegralAdSDK/BidNativeAd
    - MintegralAdSDK/RewardVideoAd
  - MintegralAdSDK/BidSplashAd (7.7.8):
    - MintegralAdSDK/BidNativeAd
    - MintegralAdSDK/SplashAd
  - MintegralAdSDK/InterstitialVideoAd (7.7.8):
    - MintegralAdSDK/NativeAd
  - MintegralAdSDK/NativeAd (7.7.8)
  - MintegralAdSDK/NewInterstitialAd (7.7.8):
    - MintegralAdSDK/InterstitialVideoAd
    - MintegralAdSDK/NativeAd
  - MintegralAdSDK/RewardVideoAd (7.7.8):
    - MintegralAdSDK/NativeAd
  - MintegralAdSDK/SplashAd (7.7.8):
    - MintegralAdSDK/NativeAd
  - MolocoSDKiOS (3.9.1)
  - nanopb (3.30910.0):
    - nanopb/decode (= 3.30910.0)
    - nanopb/encode (= 3.30910.0)
  - nanopb/decode (3.30910.0)
  - nanopb/encode (3.30910.0)
  - PromisesObjC (2.4.0)
  - PromisesSwift (2.4.0):
    - PromisesObjC (= 2.4.0)
  - RCT-Folly (2022.05.16.00):
    - boost
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - RCT-Folly/Default (= 2022.05.16.00)
  - RCT-Folly/Default (2022.05.16.00):
    - boost
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
  - RCT-Folly/Fabric (2022.05.16.00):
    - boost
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
  - RCT-Folly/Futures (2022.05.16.00):
    - boost
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - libevent
  - RCTRequired (0.73.8)
  - RCTTypeSafety (0.73.8):
    - FBLazyVector (= 0.73.8)
    - RCTRequired (= 0.73.8)
    - React-Core (= 0.73.8)
  - React (0.73.8):
    - React-Core (= 0.73.8)
    - React-Core/DevSupport (= 0.73.8)
    - React-Core/RCTWebSocket (= 0.73.8)
    - React-RCTActionSheet (= 0.73.8)
    - React-RCTAnimation (= 0.73.8)
    - React-RCTBlob (= 0.73.8)
    - React-RCTImage (= 0.73.8)
    - React-RCTLinking (= 0.73.8)
    - React-RCTNetwork (= 0.73.8)
    - React-RCTSettings (= 0.73.8)
    - React-RCTText (= 0.73.8)
    - React-RCTVibration (= 0.73.8)
  - React-callinvoker (0.73.8)
  - React-Codegen (0.73.8):
    - DoubleConversion
    - FBReactNativeSpec
    - glog
    - hermes-engine
    - RCT-Folly
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-jsi
    - React-jsiexecutor
    - React-NativeModulesApple
    - React-rncore
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
  - React-Core (0.73.8):
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-Core/Default (= 0.73.8)
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/CoreModulesHeaders (0.73.8):
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/Default (0.73.8):
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/DevSupport (0.73.8):
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-Core/Default (= 0.73.8)
    - React-Core/RCTWebSocket (= 0.73.8)
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector (= 0.73.8)
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTActionSheetHeaders (0.73.8):
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTAnimationHeaders (0.73.8):
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTBlobHeaders (0.73.8):
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTImageHeaders (0.73.8):
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTLinkingHeaders (0.73.8):
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTNetworkHeaders (0.73.8):
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTSettingsHeaders (0.73.8):
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTTextHeaders (0.73.8):
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTVibrationHeaders (0.73.8):
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTWebSocket (0.73.8):
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-Core/Default (= 0.73.8)
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-CoreModules (0.73.8):
    - RCT-Folly (= 2022.05.16.00)
    - RCTTypeSafety (= 0.73.8)
    - React-Codegen
    - React-Core/CoreModulesHeaders (= 0.73.8)
    - React-jsi (= 0.73.8)
    - React-NativeModulesApple
    - React-RCTBlob
    - React-RCTImage (= 0.73.8)
    - ReactCommon
    - SocketRocket (= 0.6.1)
  - React-cxxreact (0.73.8):
    - boost (= 1.83.0)
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-callinvoker (= 0.73.8)
    - React-debug (= 0.73.8)
    - React-jsi (= 0.73.8)
    - React-jsinspector (= 0.73.8)
    - React-logger (= 0.73.8)
    - React-perflogger (= 0.73.8)
    - React-runtimeexecutor (= 0.73.8)
  - React-debug (0.73.8)
  - React-Fabric (0.73.8):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric/animations (= 0.73.8)
    - React-Fabric/attributedstring (= 0.73.8)
    - React-Fabric/componentregistry (= 0.73.8)
    - React-Fabric/componentregistrynative (= 0.73.8)
    - React-Fabric/components (= 0.73.8)
    - React-Fabric/core (= 0.73.8)
    - React-Fabric/imagemanager (= 0.73.8)
    - React-Fabric/leakchecker (= 0.73.8)
    - React-Fabric/mounting (= 0.73.8)
    - React-Fabric/scheduler (= 0.73.8)
    - React-Fabric/telemetry (= 0.73.8)
    - React-Fabric/templateprocessor (= 0.73.8)
    - React-Fabric/textlayoutmanager (= 0.73.8)
    - React-Fabric/uimanager (= 0.73.8)
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/animations (0.73.8):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/attributedstring (0.73.8):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/componentregistry (0.73.8):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/componentregistrynative (0.73.8):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components (0.73.8):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric/components/inputaccessory (= 0.73.8)
    - React-Fabric/components/legacyviewmanagerinterop (= 0.73.8)
    - React-Fabric/components/modal (= 0.73.8)
    - React-Fabric/components/rncore (= 0.73.8)
    - React-Fabric/components/root (= 0.73.8)
    - React-Fabric/components/safeareaview (= 0.73.8)
    - React-Fabric/components/scrollview (= 0.73.8)
    - React-Fabric/components/text (= 0.73.8)
    - React-Fabric/components/textinput (= 0.73.8)
    - React-Fabric/components/unimplementedview (= 0.73.8)
    - React-Fabric/components/view (= 0.73.8)
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/inputaccessory (0.73.8):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/legacyviewmanagerinterop (0.73.8):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/modal (0.73.8):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/rncore (0.73.8):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/root (0.73.8):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/safeareaview (0.73.8):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/scrollview (0.73.8):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/text (0.73.8):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/textinput (0.73.8):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/unimplementedview (0.73.8):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/view (0.73.8):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - Yoga
  - React-Fabric/core (0.73.8):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/imagemanager (0.73.8):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/leakchecker (0.73.8):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/mounting (0.73.8):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/scheduler (0.73.8):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/telemetry (0.73.8):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/templateprocessor (0.73.8):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/textlayoutmanager (0.73.8):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric/uimanager
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/uimanager (0.73.8):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-FabricImage (0.73.8):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired (= 0.73.8)
    - RCTTypeSafety (= 0.73.8)
    - React-Fabric
    - React-graphics
    - React-ImageManager
    - React-jsi
    - React-jsiexecutor (= 0.73.8)
    - React-logger
    - React-rendererdebug
    - React-utils
    - ReactCommon
    - Yoga
  - React-graphics (0.73.8):
    - glog
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - React-Core/Default (= 0.73.8)
    - React-utils
  - React-hermes (0.73.8):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - RCT-Folly/Futures (= 2022.05.16.00)
    - React-cxxreact (= 0.73.8)
    - React-jsi
    - React-jsiexecutor (= 0.73.8)
    - React-jsinspector (= 0.73.8)
    - React-perflogger (= 0.73.8)
  - React-ImageManager (0.73.8):
    - glog
    - RCT-Folly/Fabric
    - React-Core/Default
    - React-debug
    - React-Fabric
    - React-graphics
    - React-rendererdebug
    - React-utils
  - React-jserrorhandler (0.73.8):
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - React-debug
    - React-jsi
    - React-Mapbuffer
  - React-jsi (0.73.8):
    - boost (= 1.83.0)
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
  - React-jsiexecutor (0.73.8):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-cxxreact (= 0.73.8)
    - React-jsi (= 0.73.8)
    - React-perflogger (= 0.73.8)
  - React-jsinspector (0.73.8)
  - React-logger (0.73.8):
    - glog
  - React-Mapbuffer (0.73.8):
    - glog
    - React-debug
  - react-native-adapty-sdk (3.3.1):
    - Adapty (= 3.3.4)
    - AdaptyPlugin (= 3.3.4)
    - AdaptyUI (= 3.3.4)
    - React
  - react-native-adjust (5.1.0):
    - Adjust (= 5.1.1)
    - React-Core
  - react-native-agora (4.5.2):
    - AgoraIrisRTC_iOS (= 4.5.1-build.1)
    - AgoraRtcEngine_iOS/AIAEC (= 4.5.1)
    - AgoraRtcEngine_iOS/AINS (= 4.5.1)
    - AgoraRtcEngine_iOS/RtcBasic (= 4.5.1)
    - AgoraRtcEngine_iOS/VideoCodecDec (= 4.5.1)
    - AgoraRtcEngine_iOS/VideoCodecEnc (= 4.5.1)
    - AgoraRtcEngine_iOS/VQA (= 4.5.1)
    - glog
    - RCT-Folly (= 2022.05.16.00)
    - React-Core
  - react-native-applovin-max (9.1.0):
    - AppLovinSDK (= 13.2.0)
    - glog
    - RCT-Folly (= 2022.05.16.00)
    - React-Core
  - react-native-blur (4.4.1):
    - glog
    - RCT-Folly (= 2022.05.16.00)
    - React-Core
  - react-native-config (0.11.7):
    - React
  - react-native-create-thumbnail (2.1.1):
    - React-Core
  - react-native-geolocation-service (5.3.1):
    - React
  - react-native-keep-awake (1.3.1):
    - React-Core
  - react-native-maps (1.11.3):
    - React-Core
  - react-native-netinfo (11.4.1):
    - React-Core
  - react-native-pager-view (5.4.25):
    - React-Core
  - react-native-safe-area-context (4.9.0):
    - React-Core
  - react-native-video (6.10.1):
    - glog
    - RCT-Folly (= 2022.05.16.00)
    - React-Core
    - react-native-video/Video (= 6.10.1)
  - react-native-video/Video (6.10.1):
    - glog
    - RCT-Folly (= 2022.05.16.00)
    - React-Core
  - react-native-webview (13.13.5):
    - glog
    - RCT-Folly (= 2022.05.16.00)
    - React-Core
  - React-nativeconfig (0.73.8)
  - React-NativeModulesApple (0.73.8):
    - glog
    - hermes-engine
    - React-callinvoker
    - React-Core
    - React-cxxreact
    - React-jsi
    - React-runtimeexecutor
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
  - React-perflogger (0.73.8)
  - React-RCTActionSheet (0.73.8):
    - React-Core/RCTActionSheetHeaders (= 0.73.8)
  - React-RCTAnimation (0.73.8):
    - RCT-Folly (= 2022.05.16.00)
    - RCTTypeSafety
    - React-Codegen
    - React-Core/RCTAnimationHeaders
    - React-jsi
    - React-NativeModulesApple
    - ReactCommon
  - React-RCTAppDelegate (0.73.8):
    - RCT-Folly
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-CoreModules
    - React-hermes
    - React-nativeconfig
    - React-NativeModulesApple
    - React-RCTFabric
    - React-RCTImage
    - React-RCTNetwork
    - React-runtimescheduler
    - ReactCommon
  - React-RCTBlob (0.73.8):
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-Codegen
    - React-Core/RCTBlobHeaders
    - React-Core/RCTWebSocket
    - React-jsi
    - React-NativeModulesApple
    - React-RCTNetwork
    - ReactCommon
  - React-RCTFabric (0.73.8):
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - React-Core
    - React-debug
    - React-Fabric
    - React-FabricImage
    - React-graphics
    - React-ImageManager
    - React-jsi
    - React-nativeconfig
    - React-RCTImage
    - React-RCTText
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - Yoga
  - React-RCTImage (0.73.8):
    - RCT-Folly (= 2022.05.16.00)
    - RCTTypeSafety
    - React-Codegen
    - React-Core/RCTImageHeaders
    - React-jsi
    - React-NativeModulesApple
    - React-RCTNetwork
    - ReactCommon
  - React-RCTLinking (0.73.8):
    - React-Codegen
    - React-Core/RCTLinkingHeaders (= 0.73.8)
    - React-jsi (= 0.73.8)
    - React-NativeModulesApple
    - ReactCommon
    - ReactCommon/turbomodule/core (= 0.73.8)
  - React-RCTNetwork (0.73.8):
    - RCT-Folly (= 2022.05.16.00)
    - RCTTypeSafety
    - React-Codegen
    - React-Core/RCTNetworkHeaders
    - React-jsi
    - React-NativeModulesApple
    - ReactCommon
  - React-RCTSettings (0.73.8):
    - RCT-Folly (= 2022.05.16.00)
    - RCTTypeSafety
    - React-Codegen
    - React-Core/RCTSettingsHeaders
    - React-jsi
    - React-NativeModulesApple
    - ReactCommon
  - React-RCTText (0.73.8):
    - React-Core/RCTTextHeaders (= 0.73.8)
    - Yoga
  - React-RCTVibration (0.73.8):
    - RCT-Folly (= 2022.05.16.00)
    - React-Codegen
    - React-Core/RCTVibrationHeaders
    - React-jsi
    - React-NativeModulesApple
    - ReactCommon
  - React-rendererdebug (0.73.8):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - RCT-Folly (= 2022.05.16.00)
    - React-debug
  - React-rncore (0.73.8)
  - React-runtimeexecutor (0.73.8):
    - React-jsi (= 0.73.8)
  - React-runtimescheduler (0.73.8):
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-callinvoker
    - React-cxxreact
    - React-debug
    - React-jsi
    - React-rendererdebug
    - React-runtimeexecutor
    - React-utils
  - React-utils (0.73.8):
    - glog
    - RCT-Folly (= 2022.05.16.00)
    - React-debug
  - ReactCommon (0.73.8):
    - React-logger (= 0.73.8)
    - ReactCommon/turbomodule (= 0.73.8)
  - ReactCommon/turbomodule (0.73.8):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-callinvoker (= 0.73.8)
    - React-cxxreact (= 0.73.8)
    - React-jsi (= 0.73.8)
    - React-logger (= 0.73.8)
    - React-perflogger (= 0.73.8)
    - ReactCommon/turbomodule/bridging (= 0.73.8)
    - ReactCommon/turbomodule/core (= 0.73.8)
  - ReactCommon/turbomodule/bridging (0.73.8):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-callinvoker (= 0.73.8)
    - React-cxxreact (= 0.73.8)
    - React-jsi (= 0.73.8)
    - React-logger (= 0.73.8)
    - React-perflogger (= 0.73.8)
  - ReactCommon/turbomodule/core (0.73.8):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-callinvoker (= 0.73.8)
    - React-cxxreact (= 0.73.8)
    - React-jsi (= 0.73.8)
    - React-logger (= 0.73.8)
    - React-perflogger (= 0.73.8)
  - RealmJS (20.1.0):
    - React
  - RNAppleAuthentication (2.4.1):
    - React-Core
  - RNCAsyncStorage (2.1.2):
    - React-Core
  - RNCClipboard (1.16.2):
    - React-Core
  - RNDateTimePicker (8.3.0):
    - React-Core
  - RNDeviceInfo (11.1.0):
    - React-Core
  - RNFBAnalytics (21.11.0):
    - Firebase/Analytics (= 11.8.0)
    - React-Core
    - RNFBApp
  - RNFBApp (21.11.0):
    - Firebase/CoreOnly (= 11.8.0)
    - React-Core
  - RNFBCrashlytics (21.11.0):
    - Firebase/Crashlytics (= 11.8.0)
    - FirebaseCoreExtension
    - React-Core
    - RNFBApp
  - RNFBInAppMessaging (21.11.0):
    - Firebase/InAppMessaging (= 11.8.0)
    - React-Core
    - RNFBApp
  - RNFBMessaging (21.11.0):
    - Firebase/Messaging (= 11.8.0)
    - FirebaseCoreExtension
    - React-Core
    - RNFBApp
  - RNFBRemoteConfig (21.11.0):
    - Firebase/RemoteConfig (= 11.8.0)
    - React-Core
    - RNFBApp
  - RNGestureHandler (2.15.0):
    - glog
    - RCT-Folly (= 2022.05.16.00)
    - React-Core
  - RNImageCropPicker (0.42.0):
    - React-Core
    - React-RCTImage
    - RNImageCropPicker/QBImagePickerController (= 0.42.0)
    - TOCropViewController (~> 2.7.4)
  - RNImageCropPicker/QBImagePickerController (0.42.0):
    - React-Core
    - React-RCTImage
    - TOCropViewController (~> 2.7.4)
  - RNLocalize (3.4.1):
    - React-Core
  - RNPermissions (5.2.6):
    - React-Core
  - RNReactNativeHapticFeedback (2.3.3):
    - glog
    - RCT-Folly (= 2022.05.16.00)
    - React-Core
  - RNReanimated (3.16.7):
    - glog
    - RCT-Folly (= 2022.05.16.00)
    - React-Core
    - ReactCommon/turbomodule/core
    - RNReanimated/reanimated (= 3.16.7)
    - RNReanimated/worklets (= 3.16.7)
  - RNReanimated/reanimated (3.16.7):
    - glog
    - RCT-Folly (= 2022.05.16.00)
    - React-Core
    - ReactCommon/turbomodule/core
    - RNReanimated/reanimated/apple (= 3.16.7)
  - RNReanimated/reanimated/apple (3.16.7):
    - glog
    - RCT-Folly (= 2022.05.16.00)
    - React-Core
    - ReactCommon/turbomodule/core
  - RNReanimated/worklets (3.16.7):
    - glog
    - RCT-Folly (= 2022.05.16.00)
    - React-Core
    - ReactCommon/turbomodule/core
  - RNScreens (3.29.0):
    - glog
    - RCT-Folly (= 2022.05.16.00)
    - React-Core
  - RNSentry (6.13.1):
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-Core
    - React-hermes
    - Sentry/HybridSDK (= 8.49.2)
  - RNShare (10.0.2):
    - React-Core
  - RNSVG (15.11.2):
    - React-Core
  - RNVectorIcons (10.0.0):
    - React-Core
  - Sentry/HybridSDK (8.49.2)
  - SocketRocket (0.6.1)
  - TOCropViewController (2.7.4)
  - TouchID (4.4.1):
    - React
  - UnityAds (4.14.2)
  - VisionCamera (4.6.1):
    - VisionCamera/Core (= 4.6.1)
    - VisionCamera/React (= 4.6.1)
  - VisionCamera/Core (4.6.1)
  - VisionCamera/React (4.6.1):
    - React-Core
  - VungleAds (7.5.1)
  - Yoga (1.14.0)

DEPENDENCIES:
  - "amplitude-react-native (from `../node_modules/@amplitude/analytics-react-native`)"
  - AppLovinMediationFacebookAdapter (= 6.17.1.0)
  - AppLovinMediationFyberAdapter (= 8.3.6.0)
  - AppLovinMediationGoogleAdapter (= 12.4.0.1)
  - AppLovinMediationGoogleAdManagerAdapter (= 12.4.0.1)
  - AppLovinMediationInMobiAdapter (= 10.8.3.1)
  - AppLovinMediationMintegralAdapter (= 7.7.8.0.0)
  - AppLovinMediationMolocoAdapter (= 3.9.1.1)
  - AppLovinMediationUnityAdsAdapter (= 4.14.2.0)
  - AppLovinMediationVerveAdapter (= 3.6.0.0)
  - AppLovinMediationVungleAdapter (= 7.5.1.2)
  - AppLovinSDK (= 13.2.0)
  - boost (from `../node_modules/react-native/third-party-podspecs/boost.podspec`)
  - BVLinearGradient (from `../node_modules/react-native-linear-gradient`)
  - DoubleConversion (from `../node_modules/react-native/third-party-podspecs/DoubleConversion.podspec`)
  - FBLazyVector (from `../node_modules/react-native/Libraries/FBLazyVector`)
  - FBReactNativeSpec (from `../node_modules/react-native/React/FBReactNativeSpec`)
  - Firebase/Messaging (= 11.8.0)
  - FirebaseABTesting
  - FirebaseCore
  - FirebaseCoreExtension
  - FirebaseInstallations
  - glog (from `../node_modules/react-native/third-party-podspecs/glog.podspec`)
  - GoogleDataTransport
  - GoogleUtilities
  - hermes-engine (from `../node_modules/react-native/sdks/hermes-engine/hermes-engine.podspec`)
  - jail-monkey (from `../node_modules/jail-monkey`)
  - lame (= 1.1.0)
  - libevent (~> 2.1.12)
  - nanopb
  - RCT-Folly (from `../node_modules/react-native/third-party-podspecs/RCT-Folly.podspec`)
  - RCT-Folly/Fabric (from `../node_modules/react-native/third-party-podspecs/RCT-Folly.podspec`)
  - RCTRequired (from `../node_modules/react-native/Libraries/RCTRequired`)
  - RCTTypeSafety (from `../node_modules/react-native/Libraries/TypeSafety`)
  - React (from `../node_modules/react-native/`)
  - React-callinvoker (from `../node_modules/react-native/ReactCommon/callinvoker`)
  - React-Codegen (from `build/generated/ios`)
  - React-Core (from `../node_modules/react-native/`)
  - React-Core/RCTWebSocket (from `../node_modules/react-native/`)
  - React-CoreModules (from `../node_modules/react-native/React/CoreModules`)
  - React-cxxreact (from `../node_modules/react-native/ReactCommon/cxxreact`)
  - React-debug (from `../node_modules/react-native/ReactCommon/react/debug`)
  - React-Fabric (from `../node_modules/react-native/ReactCommon`)
  - React-FabricImage (from `../node_modules/react-native/ReactCommon`)
  - React-graphics (from `../node_modules/react-native/ReactCommon/react/renderer/graphics`)
  - React-hermes (from `../node_modules/react-native/ReactCommon/hermes`)
  - React-ImageManager (from `../node_modules/react-native/ReactCommon/react/renderer/imagemanager/platform/ios`)
  - React-jserrorhandler (from `../node_modules/react-native/ReactCommon/jserrorhandler`)
  - React-jsi (from `../node_modules/react-native/ReactCommon/jsi`)
  - React-jsiexecutor (from `../node_modules/react-native/ReactCommon/jsiexecutor`)
  - React-jsinspector (from `../node_modules/react-native/ReactCommon/jsinspector-modern`)
  - React-logger (from `../node_modules/react-native/ReactCommon/logger`)
  - React-Mapbuffer (from `../node_modules/react-native/ReactCommon`)
  - react-native-adapty-sdk (from `../node_modules/react-native-adapty`)
  - react-native-adjust (from `../node_modules/react-native-adjust`)
  - react-native-agora (from `../node_modules/react-native-agora`)
  - react-native-applovin-max (from `../node_modules/react-native-applovin-max`)
  - "react-native-blur (from `../node_modules/@react-native-community/blur`)"
  - react-native-config (from `../node_modules/react-native-config`)
  - react-native-create-thumbnail (from `../node_modules/react-native-create-thumbnail`)
  - react-native-geolocation-service (from `../node_modules/react-native-geolocation-service`)
  - "react-native-keep-awake (from `../node_modules/@sayem314/react-native-keep-awake`)"
  - react-native-maps (from `../node_modules/react-native-maps`)
  - "react-native-netinfo (from `../node_modules/@react-native-community/netinfo`)"
  - react-native-pager-view (from `../node_modules/react-native-pager-view`)
  - react-native-safe-area-context (from `../node_modules/react-native-safe-area-context`)
  - react-native-video (from `../node_modules/react-native-video`)
  - react-native-webview (from `../node_modules/react-native-webview`)
  - React-nativeconfig (from `../node_modules/react-native/ReactCommon`)
  - React-NativeModulesApple (from `../node_modules/react-native/ReactCommon/react/nativemodule/core/platform/ios`)
  - React-perflogger (from `../node_modules/react-native/ReactCommon/reactperflogger`)
  - React-RCTActionSheet (from `../node_modules/react-native/Libraries/ActionSheetIOS`)
  - React-RCTAnimation (from `../node_modules/react-native/Libraries/NativeAnimation`)
  - React-RCTAppDelegate (from `../node_modules/react-native/Libraries/AppDelegate`)
  - React-RCTBlob (from `../node_modules/react-native/Libraries/Blob`)
  - React-RCTFabric (from `../node_modules/react-native/React`)
  - React-RCTImage (from `../node_modules/react-native/Libraries/Image`)
  - React-RCTLinking (from `../node_modules/react-native/Libraries/LinkingIOS`)
  - React-RCTNetwork (from `../node_modules/react-native/Libraries/Network`)
  - React-RCTSettings (from `../node_modules/react-native/Libraries/Settings`)
  - React-RCTText (from `../node_modules/react-native/Libraries/Text`)
  - React-RCTVibration (from `../node_modules/react-native/Libraries/Vibration`)
  - React-rendererdebug (from `../node_modules/react-native/ReactCommon/react/renderer/debug`)
  - React-rncore (from `../node_modules/react-native/ReactCommon`)
  - React-runtimeexecutor (from `../node_modules/react-native/ReactCommon/runtimeexecutor`)
  - React-runtimescheduler (from `../node_modules/react-native/ReactCommon/react/renderer/runtimescheduler`)
  - React-utils (from `../node_modules/react-native/ReactCommon/react/utils`)
  - ReactCommon/turbomodule/core (from `../node_modules/react-native/ReactCommon`)
  - RealmJS (from `../node_modules/realm`)
  - "RNAppleAuthentication (from `../node_modules/@invertase/react-native-apple-authentication`)"
  - "RNCAsyncStorage (from `../node_modules/@react-native-async-storage/async-storage`)"
  - "RNCClipboard (from `../node_modules/@react-native-clipboard/clipboard`)"
  - "RNDateTimePicker (from `../node_modules/@react-native-community/datetimepicker`)"
  - RNDeviceInfo (from `../node_modules/react-native-device-info`)
  - "RNFBAnalytics (from `../node_modules/@react-native-firebase/analytics`)"
  - "RNFBApp (from `../node_modules/@react-native-firebase/app`)"
  - "RNFBCrashlytics (from `../node_modules/@react-native-firebase/crashlytics`)"
  - "RNFBInAppMessaging (from `../node_modules/@react-native-firebase/in-app-messaging`)"
  - "RNFBMessaging (from `../node_modules/@react-native-firebase/messaging`)"
  - "RNFBRemoteConfig (from `../node_modules/@react-native-firebase/remote-config`)"
  - RNGestureHandler (from `../node_modules/react-native-gesture-handler`)
  - RNImageCropPicker (from `../node_modules/react-native-image-crop-picker`)
  - RNLocalize (from `../node_modules/react-native-localize`)
  - RNPermissions (from `../node_modules/react-native-permissions`)
  - RNReactNativeHapticFeedback (from `../node_modules/react-native-haptic-feedback`)
  - RNReanimated (from `../node_modules/react-native-reanimated`)
  - RNScreens (from `../node_modules/react-native-screens`)
  - "RNSentry (from `../node_modules/@sentry/react-native`)"
  - RNShare (from `../node_modules/react-native-share`)
  - RNSVG (from `../node_modules/react-native-svg`)
  - RNVectorIcons (from `../node_modules/react-native-vector-icons`)
  - TouchID (from `../node_modules/react-native-touch-id`)
  - VisionCamera (from `../node_modules/react-native-vision-camera`)
  - Yoga (from `../node_modules/react-native/ReactCommon/yoga`)

SPEC REPOS:
  https://github.com/CocoaPods/Specs.git:
    - Adapty
    - AdaptyPlugin
    - AdaptyUI
    - Adjust
    - AdjustSignature
    - AgoraInfra_iOS
    - AgoraIrisRTC_iOS
    - AgoraRtcEngine_iOS
    - AppLovinMediationFacebookAdapter
    - AppLovinMediationFyberAdapter
    - AppLovinMediationGoogleAdapter
    - AppLovinMediationGoogleAdManagerAdapter
    - AppLovinMediationInMobiAdapter
    - AppLovinMediationMintegralAdapter
    - AppLovinMediationMolocoAdapter
    - AppLovinMediationUnityAdsAdapter
    - AppLovinMediationVerveAdapter
    - AppLovinMediationVungleAdapter
    - AppLovinSDK
    - ATOM-Standalone
    - FBAudienceNetwork
    - Firebase
    - FirebaseABTesting
    - FirebaseAnalytics
    - FirebaseCore
    - FirebaseCoreExtension
    - FirebaseCoreInternal
    - FirebaseCrashlytics
    - FirebaseInAppMessaging
    - FirebaseInstallations
    - FirebaseMessaging
    - FirebaseRemoteConfig
    - FirebaseRemoteConfigInterop
    - FirebaseSessions
    - FirebaseSharedSwift
    - fmt
    - Fyber_Marketplace_SDK
    - Google-Mobile-Ads-SDK
    - GoogleAppMeasurement
    - GoogleDataTransport
    - GoogleUserMessagingPlatform
    - GoogleUtilities
    - HyBid
    - InMobiSDK
    - lame
    - libevent
    - MintegralAdSDK
    - MolocoSDKiOS
    - nanopb
    - PromisesObjC
    - PromisesSwift
    - Sentry
    - SocketRocket
    - TOCropViewController
    - UnityAds
    - VungleAds

EXTERNAL SOURCES:
  amplitude-react-native:
    :path: "../node_modules/@amplitude/analytics-react-native"
  boost:
    :podspec: "../node_modules/react-native/third-party-podspecs/boost.podspec"
  BVLinearGradient:
    :path: "../node_modules/react-native-linear-gradient"
  DoubleConversion:
    :podspec: "../node_modules/react-native/third-party-podspecs/DoubleConversion.podspec"
  FBLazyVector:
    :path: "../node_modules/react-native/Libraries/FBLazyVector"
  FBReactNativeSpec:
    :path: "../node_modules/react-native/React/FBReactNativeSpec"
  glog:
    :podspec: "../node_modules/react-native/third-party-podspecs/glog.podspec"
  hermes-engine:
    :podspec: "../node_modules/react-native/sdks/hermes-engine/hermes-engine.podspec"
    :tag: hermes-2024-04-29-RNv0.73.8-644c8be78af1eae7c138fa4093fb87f0f4f8db85
  jail-monkey:
    :path: "../node_modules/jail-monkey"
  RCT-Folly:
    :podspec: "../node_modules/react-native/third-party-podspecs/RCT-Folly.podspec"
  RCTRequired:
    :path: "../node_modules/react-native/Libraries/RCTRequired"
  RCTTypeSafety:
    :path: "../node_modules/react-native/Libraries/TypeSafety"
  React:
    :path: "../node_modules/react-native/"
  React-callinvoker:
    :path: "../node_modules/react-native/ReactCommon/callinvoker"
  React-Codegen:
    :path: build/generated/ios
  React-Core:
    :path: "../node_modules/react-native/"
  React-CoreModules:
    :path: "../node_modules/react-native/React/CoreModules"
  React-cxxreact:
    :path: "../node_modules/react-native/ReactCommon/cxxreact"
  React-debug:
    :path: "../node_modules/react-native/ReactCommon/react/debug"
  React-Fabric:
    :path: "../node_modules/react-native/ReactCommon"
  React-FabricImage:
    :path: "../node_modules/react-native/ReactCommon"
  React-graphics:
    :path: "../node_modules/react-native/ReactCommon/react/renderer/graphics"
  React-hermes:
    :path: "../node_modules/react-native/ReactCommon/hermes"
  React-ImageManager:
    :path: "../node_modules/react-native/ReactCommon/react/renderer/imagemanager/platform/ios"
  React-jserrorhandler:
    :path: "../node_modules/react-native/ReactCommon/jserrorhandler"
  React-jsi:
    :path: "../node_modules/react-native/ReactCommon/jsi"
  React-jsiexecutor:
    :path: "../node_modules/react-native/ReactCommon/jsiexecutor"
  React-jsinspector:
    :path: "../node_modules/react-native/ReactCommon/jsinspector-modern"
  React-logger:
    :path: "../node_modules/react-native/ReactCommon/logger"
  React-Mapbuffer:
    :path: "../node_modules/react-native/ReactCommon"
  react-native-adapty-sdk:
    :path: "../node_modules/react-native-adapty"
  react-native-adjust:
    :path: "../node_modules/react-native-adjust"
  react-native-agora:
    :path: "../node_modules/react-native-agora"
  react-native-applovin-max:
    :path: "../node_modules/react-native-applovin-max"
  react-native-blur:
    :path: "../node_modules/@react-native-community/blur"
  react-native-config:
    :path: "../node_modules/react-native-config"
  react-native-create-thumbnail:
    :path: "../node_modules/react-native-create-thumbnail"
  react-native-geolocation-service:
    :path: "../node_modules/react-native-geolocation-service"
  react-native-keep-awake:
    :path: "../node_modules/@sayem314/react-native-keep-awake"
  react-native-maps:
    :path: "../node_modules/react-native-maps"
  react-native-netinfo:
    :path: "../node_modules/@react-native-community/netinfo"
  react-native-pager-view:
    :path: "../node_modules/react-native-pager-view"
  react-native-safe-area-context:
    :path: "../node_modules/react-native-safe-area-context"
  react-native-video:
    :path: "../node_modules/react-native-video"
  react-native-webview:
    :path: "../node_modules/react-native-webview"
  React-nativeconfig:
    :path: "../node_modules/react-native/ReactCommon"
  React-NativeModulesApple:
    :path: "../node_modules/react-native/ReactCommon/react/nativemodule/core/platform/ios"
  React-perflogger:
    :path: "../node_modules/react-native/ReactCommon/reactperflogger"
  React-RCTActionSheet:
    :path: "../node_modules/react-native/Libraries/ActionSheetIOS"
  React-RCTAnimation:
    :path: "../node_modules/react-native/Libraries/NativeAnimation"
  React-RCTAppDelegate:
    :path: "../node_modules/react-native/Libraries/AppDelegate"
  React-RCTBlob:
    :path: "../node_modules/react-native/Libraries/Blob"
  React-RCTFabric:
    :path: "../node_modules/react-native/React"
  React-RCTImage:
    :path: "../node_modules/react-native/Libraries/Image"
  React-RCTLinking:
    :path: "../node_modules/react-native/Libraries/LinkingIOS"
  React-RCTNetwork:
    :path: "../node_modules/react-native/Libraries/Network"
  React-RCTSettings:
    :path: "../node_modules/react-native/Libraries/Settings"
  React-RCTText:
    :path: "../node_modules/react-native/Libraries/Text"
  React-RCTVibration:
    :path: "../node_modules/react-native/Libraries/Vibration"
  React-rendererdebug:
    :path: "../node_modules/react-native/ReactCommon/react/renderer/debug"
  React-rncore:
    :path: "../node_modules/react-native/ReactCommon"
  React-runtimeexecutor:
    :path: "../node_modules/react-native/ReactCommon/runtimeexecutor"
  React-runtimescheduler:
    :path: "../node_modules/react-native/ReactCommon/react/renderer/runtimescheduler"
  React-utils:
    :path: "../node_modules/react-native/ReactCommon/react/utils"
  ReactCommon:
    :path: "../node_modules/react-native/ReactCommon"
  RealmJS:
    :path: "../node_modules/realm"
  RNAppleAuthentication:
    :path: "../node_modules/@invertase/react-native-apple-authentication"
  RNCAsyncStorage:
    :path: "../node_modules/@react-native-async-storage/async-storage"
  RNCClipboard:
    :path: "../node_modules/@react-native-clipboard/clipboard"
  RNDateTimePicker:
    :path: "../node_modules/@react-native-community/datetimepicker"
  RNDeviceInfo:
    :path: "../node_modules/react-native-device-info"
  RNFBAnalytics:
    :path: "../node_modules/@react-native-firebase/analytics"
  RNFBApp:
    :path: "../node_modules/@react-native-firebase/app"
  RNFBCrashlytics:
    :path: "../node_modules/@react-native-firebase/crashlytics"
  RNFBInAppMessaging:
    :path: "../node_modules/@react-native-firebase/in-app-messaging"
  RNFBMessaging:
    :path: "../node_modules/@react-native-firebase/messaging"
  RNFBRemoteConfig:
    :path: "../node_modules/@react-native-firebase/remote-config"
  RNGestureHandler:
    :path: "../node_modules/react-native-gesture-handler"
  RNImageCropPicker:
    :path: "../node_modules/react-native-image-crop-picker"
  RNLocalize:
    :path: "../node_modules/react-native-localize"
  RNPermissions:
    :path: "../node_modules/react-native-permissions"
  RNReactNativeHapticFeedback:
    :path: "../node_modules/react-native-haptic-feedback"
  RNReanimated:
    :path: "../node_modules/react-native-reanimated"
  RNScreens:
    :path: "../node_modules/react-native-screens"
  RNSentry:
    :path: "../node_modules/@sentry/react-native"
  RNShare:
    :path: "../node_modules/react-native-share"
  RNSVG:
    :path: "../node_modules/react-native-svg"
  RNVectorIcons:
    :path: "../node_modules/react-native-vector-icons"
  TouchID:
    :path: "../node_modules/react-native-touch-id"
  VisionCamera:
    :path: "../node_modules/react-native-vision-camera"
  Yoga:
    :path: "../node_modules/react-native/ReactCommon/yoga"

SPEC CHECKSUMS:
  Adapty: 5a4d9feb206a870456e87043541ba1572f2f6273
  AdaptyPlugin: f02a6637a091efd8c1a49c122fd30eddba4a3142
  AdaptyUI: 6d0be19e8b24d2cbfb00b44b774c957656c20414
  Adjust: 5f5e998cf1446d44d93f91ec8b7b7dd40eaa922d
  AdjustSignature: 23b9e5d4adcadffc303bb6b410fde617dd88504f
  AgoraInfra_iOS: 65e11a2183ab7836258768868d06058c22701b13
  AgoraIrisRTC_iOS: dd3d957c25be6bd2b2a5d03fe706ebe8a46909f0
  AgoraRtcEngine_iOS: 5092a058c7b2842db39d8ca614d451af6f84969a
  amplitude-react-native: 7c5f44acf7840fd74a7d68ac2ee592a74da2fe92
  AppLovinMediationFacebookAdapter: 413c7188e2a21e4c9256aa2b096ddc27240b1443
  AppLovinMediationFyberAdapter: c75e18b1b65dcf194ff23822262ea9823a6a8acd
  AppLovinMediationGoogleAdapter: 4ce1be86357df6db5f8e7c02d6cb784a554ea1ff
  AppLovinMediationGoogleAdManagerAdapter: 6ba1c725f3922fff8e32af5d33bda1e7bdb832e9
  AppLovinMediationInMobiAdapter: d48f3ea94e88f04c86215edf1f56436bfe2a51b5
  AppLovinMediationMintegralAdapter: 5f64aa8427ba19efea2e0d583abc22dc4633705b
  AppLovinMediationMolocoAdapter: 13b9a6b0cefdbdc9850d18863987393192c0ca79
  AppLovinMediationUnityAdsAdapter: 4e4045a920c11e08ba16208aeffa38cc6c3d7376
  AppLovinMediationVerveAdapter: c70eccb3fd30b2da272a9c23e1906dbf12bb7f74
  AppLovinMediationVungleAdapter: dec51ed10eb5bf5dd26396d73c2bd43e1c09eb80
  AppLovinSDK: 1eb88a82b4feea9f3696ce9cc1e3343c4997ee12
  ATOM-Standalone: 37780192e3490b9bceec5c42bcb8d3f801683320
  boost: d3f49c53809116a5d38da093a8aa78bf551aed09
  BVLinearGradient: cb006ba232a1f3e4f341bb62c42d1098c284da70
  DoubleConversion: fea03f2699887d960129cc54bba7e52542b6f953
  FBAudienceNetwork: eb3ffbf2b35df25e21e163657044ffef66616a40
  FBLazyVector: df34a309e356a77581809834f6ec3fbe7153f620
  FBReactNativeSpec: bbe8b686178e5ce03d1d8a356789f211f91f31b8
  Firebase: d80354ed7f6df5f9aca55e9eb47cc4b634735eaf
  FirebaseABTesting: 7d6eee42b9137541eac2610e5fea3568d956707a
  FirebaseAnalytics: 4fd42def128146e24e480e89f310e3d8534ea42b
  FirebaseCore: 99fe0c4b44a39f37d99e6404e02009d2db5d718d
  FirebaseCoreExtension: 3d3f2017a00d06e09ab4ebe065391b0bb642565e
  FirebaseCoreInternal: df24ce5af28864660ecbd13596fc8dd3a8c34629
  FirebaseCrashlytics: a1102c035f18d5dd94a5969ee439c526d0c9e313
  FirebaseInAppMessaging: e332d3534d11bc5e4f800953dd4e1fff9866f53b
  FirebaseInstallations: 6c963bd2a86aca0481eef4f48f5a4df783ae5917
  FirebaseMessaging: 487b634ccdf6f7b7ff180fdcb2a9935490f764e8
  FirebaseRemoteConfig: f63724461fd97f0d62f20021314b59388f3e8ef8
  FirebaseRemoteConfigInterop: 98897a64aa372eac3c5b3fe2816594ccfaac55ef
  FirebaseSessions: c4d40a97f88f9eaff2834d61b4fea0a522d62123
  FirebaseSharedSwift: 672954eac7b141d6954fab9a32d45d6b1d922df8
  fmt: ff9d55029c625d3757ed641535fd4a75fedc7ce9
  Fyber_Marketplace_SDK: 231146f679a38b29fe40c855b51c2a7dc085c116
  glog: c5d68082e772fa1c511173d6b30a9de2c05a69a2
  Google-Mobile-Ads-SDK: db00a3836f288f94e52114f312b56fe066699d82
  GoogleAppMeasurement: fc0817122bd4d4189164f85374e06773b9561896
  GoogleDataTransport: aae35b7ea0c09004c3797d53c8c41f66f219d6a7
  GoogleUserMessagingPlatform: f8d0cdad3ca835406755d0a69aa634f00e76d576
  GoogleUtilities: 26a3abef001b6533cf678d3eb38fd3f614b7872d
  hermes-engine: b12d9bb1b7cee546f5e48212e7ea7e3c1665a367
  HyBid: 2018ab41ab8db1066502e45c712164c92494ff6d
  InMobiSDK: 174c7910f27fb860e4f2204d621ef724efefc819
  jail-monkey: 9298d04aa09f8c9cdcf04e7bf597ea423b52b5bf
  lame: 4761fc34a97c3c974df676c55647d6733574cf4d
  libevent: 4049cae6c81cdb3654a443be001fb9bdceff7913
  MintegralAdSDK: 07e018290689b906499d8285654bba42681cafcf
  MolocoSDKiOS: 1474abe1f4cde14b95ce2a38f4fb917a80e40e74
  nanopb: fad817b59e0457d11a5dfbde799381cd727c1275
  PromisesObjC: f5707f49cb48b9636751c5b2e7d227e43fba9f47
  PromisesSwift: 9d77319bbe72ebf6d872900551f7eeba9bce2851
  RCT-Folly: cd21f1661364f975ae76b3308167ad66b09f53f5
  RCTRequired: 0c7f03a41ee32dec802c74c341e317a4165973d5
  RCTTypeSafety: 57698bb7fcde424922e201dab377f496a08a63e3
  React: 64c0c83924460a3d34fa5857ca2fd3ed2a69b581
  React-callinvoker: c0129cd7b8babac64b3d3515e72a8b2e743d261e
  React-Codegen: a8069ecb98f0f608cb8b6b25c7580201a12bc062
  React-Core: df8ae6f929fb71ba3f138d3810eea0078df0c7c9
  React-CoreModules: 52689fad6e3ee38cda87237c8f8bfb07febd2c48
  React-cxxreact: a77dc838e2c6f8a3b0253c78dc9eed2224512c00
  React-debug: 9a287572a7a36429da0509664ce3d1931f39f3c3
  React-Fabric: 31ccf400c1809363c0c1b2b16444c022856b4206
  React-FabricImage: b277312bedd2a22bb12aa99b05f2e57d4b5b1517
  React-graphics: db2b875c768cf1b95160be53099214796080d4fb
  React-hermes: c58baecd72d4cc4138ff8a2c812dbe57394d5568
  React-ImageManager: 948780228b32f5eeab1151755cee740f32b77437
  React-jserrorhandler: 6b30172ca169521afdec8e8d968809da0862a5e4
  React-jsi: 2e3c3efe487bae67b4bd9b0d81efc67f87ae2990
  React-jsiexecutor: efcdc14052b16e55b029c9179962e1225ccf2601
  React-jsinspector: 1729acf5ffe2d4439d698da25fddf0c75d07d1a1
  React-logger: 7904abd7e1299754785e356af15ec9d1e9439901
  React-Mapbuffer: d56122e84907e9a6dad428c83cd2bd0d3dc64f71
  react-native-adapty-sdk: 9dd1758b021edf5cbd12fbffc2e15786cf337846
  react-native-adjust: 797a7c92ef21b156b87ce96e8074b7d0ded8066c
  react-native-agora: 1cdc776d1f7c97380ddd0beef9803ef039fcd623
  react-native-applovin-max: 33a7572b2cee91112223fdeb9fa8a809fd4e1ab3
  react-native-blur: 9a53b0cc99ebd9d2dcd48f30cb61d0e32392a17d
  react-native-config: ee5981bc202f737355e75e014a16cb95a9fcb61a
  react-native-create-thumbnail: 733160dca9a548262e1030e7fcdae90a93d203ca
  react-native-geolocation-service: 32b2c2a3b91e70ce2a8d0c684801aaeb0a07e0ec
  react-native-keep-awake: 03b74eebe4f2bb5e8478fc8f420651a92463b6f8
  react-native-maps: a7783e616e0c3f2ee109ae315a15fb3c8608b1c3
  react-native-netinfo: cec9c4e86083cb5b6aba0e0711f563e2fbbff187
  react-native-pager-view: 873aef831fb4fe6e1a2e1ba7a79857e240dea380
  react-native-safe-area-context: 435f4c13ac75ceed6135382ee77d57d1a5b5b2d6
  react-native-video: 61aaf41c43e420732f7acae17e87d421dd679e9e
  react-native-webview: 2038f781e99d2c75c90da7d38d37675ef0a932df
  React-nativeconfig: 2e44d0d2dd222b12a5183f4bcaa4a91881497acb
  React-NativeModulesApple: b70b38573773f2e9e342a1cf9ca5e36f5db55419
  React-perflogger: f9367428cf475f4606b5965c1d5a71781bb95299
  React-RCTActionSheet: 39b3248276c7f98e455aebb0cdd473a35c6d5082
  React-RCTAnimation: a8c1773b494690270da2574f238f7c49a35bb09b
  React-RCTAppDelegate: f00f261751a149e0395890b9780879c67b1dca14
  React-RCTBlob: 59f8e06cda89868b3a1401c6a19eb5289740a7c8
  React-RCTFabric: e8a3593890e6c890e253bc1f285cb99fb11e3c5e
  React-RCTImage: 00d32dcef0eb07336fbf791b4c23d7a2a65c9abe
  React-RCTLinking: d0d3530def72c304797bde151cb83d97a177e276
  React-RCTNetwork: e2f7be6fc795210de32f0b296269615c0e176e91
  React-RCTSettings: c71bfa4a2f9ae331a595090470e394000ba1035c
  React-RCTText: 13d54d357ece4e63b1c058b8cdc629b3a94e8320
  React-RCTVibration: 91ce401023cc0c382f25c0de6d41b2ab4599efcf
  React-rendererdebug: 3f1b4415498f0ad7ad830b13ad59b3f7577725d2
  React-rncore: e4514e3d953d0ad476a3e5dd999e16d68ebae2e4
  React-runtimeexecutor: 1fb11b17da6dcf79da6f301ab54fcb51545bab6e
  React-runtimescheduler: 8747bbc38295b83b13bed103c1a236331a279fff
  React-utils: 1630c10ad6b9fe2bf7bfa82f999f58f99f3807d6
  ReactCommon: ed4e27a1815f5927b5c807c5818b7c5b4dd4ee97
  RealmJS: 9fd51c849eb552ade9f7b11db42a319b4f6cab4c
  RNAppleAuthentication: c3dddf5918126c9aae85dc2e2ce9fb87835e9e04
  RNCAsyncStorage: b9f5f78da5d16a853fe3dc22e8268d932fc45a83
  RNCClipboard: e1d17c9d093d8129ef50b39b63a17a0e8ccd0ade
  RNDateTimePicker: 29264364ea7b8cc0fb355b3843cf276a4ff78966
  RNDeviceInfo: 900bd20e1fd3bfd894e7384cc4a83880c0341bd3
  RNFBAnalytics: 1dfa1e8dd307e0d75b8fce82e308fd791a6d8163
  RNFBApp: b5626640e0f4b4fe5be4f44375df703c0d62ee4b
  RNFBCrashlytics: 5ab7d01e4edf621a35b0740227d8eee60c4271e6
  RNFBInAppMessaging: e5ed74e2fab83cf6f985b22ba0610182b03019d6
  RNFBMessaging: 8e38f5ca846497f8a9c91d33f311c00ca52d119c
  RNFBRemoteConfig: b6aa114244ae1409b9ef04d373f729585534d1a5
  RNGestureHandler: 4fe9dbe20b22c597b3ac3eae5d87876dc6b6fff1
  RNImageCropPicker: b0afedeefd8655254a15c34103718455fde6ce59
  RNLocalize: 15463c4d79c7da45230064b4adcf5e9bb984667e
  RNPermissions: cd0da746f58e99408ca7568666b2fc8d0d7eb930
  RNReactNativeHapticFeedback: 9d07fb28f9ef1e81440181602954cb3c78d30184
  RNReanimated: 88dfb3d4b761e093b5a794a893785e15f96db949
  RNScreens: 59f615aa0782077560c8eea46c2daec3a716dba9
  RNSentry: 73a0ca6d5690ccfb6081dc6ab2e73e9d3db450ca
  RNShare: 01c717e457489779d16293d2afef36995aadd001
  RNSVG: 36ab724902de0406127ef8d63d940c055e2a1442
  RNVectorIcons: d1ddcf27e5229b7b3ad526bdb9a3a9c3b3742584
  Sentry: 47021097466aa130802420d485a34da445963d99
  SocketRocket: f32cd54efbe0f095c4d7594881e52619cfe80b17
  TOCropViewController: 80b8985ad794298fb69d3341de183f33d1853654
  TouchID: 6d9af299007675845fe9e23bd2701f4354613366
  UnityAds: eabbbb3014326e45dc221a467fba16960e7b73e3
  VisionCamera: 0aa13a6fe8b0eea99357d94b57a284171dd404b2
  VungleAds: f7143149d6160211c6d462453adce125160e4283
  Yoga: e5b887426cee15d2a326bdd34afc0282fc0486ad

PODFILE CHECKSUM: aeed041afbac024df29aee9a00ff7a641ff1cc34

COCOAPODS: 1.14.3
