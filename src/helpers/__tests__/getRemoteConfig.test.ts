import * as getRemoteConfigModule from "../getRemoteConfig";

describe("getRemoteConfig helper", () => {
    // Mock config and addConfig if needed
    beforeEach(() => {
        jest.resetModules();
    });

    it("should export expected functions", () => {
        expect(typeof getRemoteConfigModule.addConfig).toBe("function");
        expect(typeof getRemoteConfigModule.getRemoteConfig).toBe("function");
    });

    it("getRemoteConfig returns default value for unknown key", async () => {
        const result =
            await getRemoteConfigModule.getRemoteConfig("unknown_key");
        expect(result).toBeUndefined();
    });

    it("addConfig and getRemoteConfig work for custom key", async () => {
        getRemoteConfigModule.addConfig(
            "test_key",
            "default",
            async (key, alt) => {
                return alt + "_custom";
            },
        );
        const result = await getRemoteConfigModule.getRemoteConfig("test_key");
        expect(result).toBe("default_custom");
    });

    it("getRemoteConfig falls back to default if getter throws", async () => {
        getRemoteConfigModule.addConfig("fail_key", "fallback", async () => {
            throw new Error("fail");
        });
        const result = await getRemoteConfigModule.getRemoteConfig("fail_key");
        expect(result).toBe("fallback");
    });

    it("getRemoteConfig returns parsed value if getter returns JSON string", async () => {
        getRemoteConfigModule.addConfig("json_key", {}, async () => {
            return { foo: "bar" };
        });
        const result = await getRemoteConfigModule.getRemoteConfig("json_key");
        expect(result).toEqual({ foo: "bar" });
    });

    // Add more tests for edge cases as needed
});
