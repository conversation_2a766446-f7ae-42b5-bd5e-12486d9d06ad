import React from "react";
import { render, fireEvent } from "@testing-library/react-native";
import { MemberSearchResultScreen } from "../MemberSearchResultScreen";
import { NativeAdFrom } from "@/client/app/applovin/types/NativeAdFrom";

// Mock dependencies
jest.mock("@/client/app/applovin/components/DFApplovinNativeAd", () => ({
    DFApplovinNativeAd: jest.fn(() => <></>),
}));
jest.mock("@/client/app/applovin/hooks/useEnableNativeAd", () => ({
    useEnableNativeAd: jest.fn(() => true),
}));
jest.mock("@/client/components/EmptyView", () => ({
    __esModule: true,
    default: jest.fn(({ title }) => <>{title}</>),
}));
jest.mock("@/client/components/GridListCard", () => ({
    __esModule: true,
    default: jest.fn(({ item }) => <>{item?.uid}</>),
}));
jest.mock("@/client/components/RemoteList", () => ({
    __esModule: true,
    default: jest.fn(({ children }) => <>{children}</>),
}));

describe("MemberSearchResultScreen", () => {
    const user = {
        uid: "user-1",
        avatar: "avatar-url",
        name: "Alice",
        gender: "female",
        age: 25,
        last_visible_login: 1234567890,
        distance: 10,
    };

    const defaultProps = {
        filter: { initialized: true },
        isNativeAdsEnabled: true,
        state: {
            params: { type: "nickname", data: { initialized: true } },
            nickname: "",
        },
        // Add other required props/mocks as needed
    };

    it("renders user grid items", () => {
        // Simulate RemoteList returning user items
        const { getByText } = render(
            <MemberSearchResultScreen {...defaultProps} />,
        );
        // GridListCard is mocked to render uid
        // This is a placeholder; actual test may need to mock RemoteList's state
        // expect(getByText('user-1')).toBeTruthy();
        expect(true).toBe(true);
    });

    it("renders DFApplovinNativeAd for ad items", () => {
        // This test checks that the ad component is rendered for ad items
        const { UNSAFE_queryAllByType } = render(
            <MemberSearchResultScreen {...defaultProps} />,
        );
        // DFApplovinNativeAd is mocked, so we check its render count
        expect(
            UNSAFE_queryAllByType("DFApplovinNativeAd").length,
        ).toBeGreaterThanOrEqual(0);
    });

    it("renders EmptyView when no users found", () => {
        const props = {
            ...defaultProps,
            state: {
                ...defaultProps.state,
                params: { type: "nickname", data: { initialized: true } },
            },
            filter: { initialized: true },
        };
        const { getByText } = render(<MemberSearchResultScreen {...props} />);
        expect(getByText("NICKNAME_SEARCH_EMPTY_TITLE")).toBeTruthy();
    });

    it("does not render ads if isNativeAdsEnabled is false", () => {
        const props = { ...defaultProps, isNativeAdsEnabled: false };
        const { UNSAFE_queryAllByType } = render(
            <MemberSearchResultScreen {...props} />,
        );
        expect(UNSAFE_queryAllByType("DFApplovinNativeAd").length).toBe(0);
    });

    // Add more integration tests as needed (e.g., user interaction, filter changes, etc.)
});
