import type { IUserSearchFilter } from '@/client/app/search/interfaces/IUserSearchFilter';
import type { IUserSearchResult } from '@/client/app/search/interfaces/IUserSearchResult';
import type { IPageLoader } from '@/shared/interfaces/IPageLoader';
import type { IGeoLocation } from '@/client/interfaces/IGeoLocation';
import React, { useRef, useCallback, useEffect } from 'react';
import { useDispatch } from 'react-redux';
import connect from '@/client/store/connect';
import { Animated, Easing, View, ListRenderItem, ScrollViewProps, Platform } from 'react-native';
import Screen from '@/client/components/Screen';
import RemoteList from '@/client/components/RemoteList';
import EmptyView from '@/client/components/EmptyView';
import Spinner from '@/client/components/Spinner';
import RadarAni from '@/client/app/search/components/RadarAni';
import SearchInput from '@/client/app/search/components/SearchInput';
import GridListCard from '@/client/components/GridListCard';
import AvatarReminderBanner from '@/client/components/Banner/AvatarReminderBanner';
import ShareLocationBanner from '@/client/components/Banner/ShareLocationBanner';
import RiseUpBanner from '@/client/app/rise-up/components/RiseUpBanner';
import SearchFilterUpdater from '@/client/app/search/components/SearchFilterUpdater';
import UserIgnoredFilter from '@/client/app/settings/components/UserIgnoredFilter';
import { getMemberSearchFilter } from '@/client/app/search/selectors';
import {
    doNavProfile,
    doNavMemberSearchFilter,
    doNavReport,
} from '@/client/app/nav/actions';
import searchModel from '@/client/app/search/model';
import DevInfo from '@/client/config/device';
import Styles from '@/client/config/styles';
import i18n, { formatRange } from '@/client/core/intl';
import property from 'lodash/property';
import uniqBy from 'lodash/uniqBy';
import { compact } from 'lodash';
import { SecondaryLocationBanner } from '@/client/components/Banner/SecondaryLocationBanner';
import showActionSheet from '@/client/helpers/showActionSheet';
import reportModel from '@/client/app/report/model';
import ignoreModel from '@/client/app/ignore/model';
import { getAccountUid } from '@/client/app/auth/selectors';
import { getNativeAdsEntries } from '@/client/app/status/selectors';
import { DFApplovinNativeAd } from '@/client/app/applovin/components/DFApplovinNativeAd';
import { NativeAdFrom } from '@/client/app/applovin/types/NativeAdFrom';
import { useEnableNativeAd } from '@/client/app/applovin/hooks/useEnableNativeAd';
import { useSafeState } from '@/client/hooks/useSafeState';

interface Props {
    filter: IUserSearchFilter;
    uid: string;
    isNativeAdsEnabled?: boolean;
}

interface State {
    params: Params;
    nickname: string;
}

type Params = {
    type: 'nickname';
    data: {
        nickname: string;
        location: Nullable<IGeoLocation>;
    };
} | {
    type: 'filter';
    data: IUserSearchFilter;
};

type IUserItem = {
    type: 'user';
    data: IUserSearchResult;
    key: string;
};

type IAdItem = {
    type: 'ad';
    data: null;
    key: string;
};

type IGridItem = IUserItem | IAdItem;

const isIOS = Platform.OS === 'ios';
const ITEM_PADDING = 5;
const LIST_WIDTH = DevInfo.WindowWidth - ITEM_PADDING * 2;
const ITEM_WIDTH = LIST_WIDTH / 2;
const AD_PER_USER = 3;

const isUserItem = (item: IGridItem): item is IUserItem => item.type === 'user';

export const MemberSearchResultScreen: React.FC<Props> = (props) => {
    const dispatch = useDispatch();
    const inputVisibleRef = useRef<boolean>(isIOS ? false : true);
    const animations = useRef({
        searchScale: new Animated.Value(isIOS ? 0 : 1),
        cancelBtnWidth: new Animated.Value(0),
    }).current;

    const refList = useRef<RemoteList<IGridItem, number, Params>>(null);
    const refUpdater = useRef<() => any>(null);

    const [state, setSafeState] = useSafeState<State>({
        params: { type: 'filter', data: props.filter },
        nickname: '',
    });

    const isOwner = useCallback((id: string) => {
        return props.uid === id;
    }, [props.uid]);

    const hideSearch = useCallback(() => {
        inputVisibleRef.current = isIOS ? false : true;
        Animated.sequence(compact([
            isIOS && Animated.timing(animations.searchScale, {
                useNativeDriver: false,
                isInteraction: false,
                easing: Easing.linear,
                duration: 200,
                toValue: 0,
            }),
            Animated.timing(animations.cancelBtnWidth, {
                useNativeDriver: false,
                isInteraction: false,
                duration: 200,
                toValue: 0,
            }),
        ])).start(() => {
            setSafeState(prevState => ({ ...prevState, nickname: '' }));
        });
    }, [animations, setSafeState]);

    const load: IPageLoader<IUserSearchResult, number, Params> = useCallback(async (page, params) => {
        if (params.type === 'filter' && !page) {
            hideSearch();
        }

        if (params.type === 'filter' && !params.data.initialized) {
            return [null, []];
        }

        const action = await dispatch(params.type === 'nickname' ? (
            searchModel.doSearchMemberByNickname(params.data, page)
        ) : (
            searchModel.doGetMemberSearchResult(params.data, page)
        ));
        if (action.error) {
            throw action.payload;
        }

        const { next_page, users } = action.payload;
        return [next_page, users, (e: any) => uniqBy(e, property('uid'))];
    }, [dispatch, hideSearch]);

    const wrappedLoad: IPageLoader<IGridItem, number, Params> = useCallback(async (page, params) => {
        const [nextPage, users,] = await load(page, params);

        // If no users or error, return as-is
        if (!users || !users.length) {
            return [nextPage, []];
        }

        if (!props.isNativeAdsEnabled) {
            return [nextPage, users.map(user => ({ type: 'user', data: user, key: user.uid }))];
        }

        const gridItems: IGridItem[] = [];

        users.forEach((user, index) => {
            const position = page ? page * 20 + index : index;

            if (position > 0 && position % AD_PER_USER === 0) {
                gridItems.push({ type: 'ad', data: null, key: `ad-${position}` });
            }

            gridItems.push({ type: 'user', data: user, key: user.uid });
        });

        return [nextPage, gridItems];
    }, [load, props.isNativeAdsEnabled]);

    const loadMore = useCallback(() => {
        refList.current?.loadMore();
    }, []);

    const getUsers = useCallback(() => {
        return refList.current?.state.list
            .filter(isUserItem)
            .map((item: IUserItem) => {
                const user = item.data;
                return { uid: user.uid, avatar: user.avatar, nickname: user.name };
            }) || null;
    }, []);

    const byFilter = useCallback(async () => {
        setSafeState(prevState => ({
            ...prevState,
            params: { type: 'filter', data: props.filter }
        }));
    }, [setSafeState, props.filter]);

    const byNickname = useCallback(() => {
        setSafeState(prevState => {
            const nickname = prevState.nickname?.trim();
            const { location } = props.filter;
            return {
                ...prevState,
                params: { type: 'nickname', data: { nickname, location } }
            };
        });
        Animated.timing(animations.cancelBtnWidth, {
            useNativeDriver: false,
            isInteraction: false,
            easing: Easing.ease,
            duration: 400,
            toValue: 1,
        }).start();
    }, [setSafeState, props.filter, animations]);

    const onScroll: ScrollViewProps['onScroll'] = useCallback((event: any) => {
        const { y } = event.nativeEvent.contentOffset;
        if (inputVisibleRef.current) {
            if (y > 0 && state.params.type === 'filter') {
                hideSearch();
            }
        } else if (y <= 0) {
            const scale = Math.min(1, (y / -40) * 1);
            animations.searchScale.setValue(scale);
            inputVisibleRef.current = scale === 1;
        }
    }, [state.params.type, hideSearch, animations]);

    const onPressItem = useCallback((item: IUserSearchResult) => {
        dispatch(doNavProfile(item, loadMore, getUsers));
    }, [dispatch, loadMore, getUsers]);

    const onLongPress = useCallback((item: IUserSearchResult) => {
        const { uid } = item;
        const isOwnerResult = isOwner(uid);
        showActionSheet([
            [i18n('REPORT'), () => {
                dispatch(doNavReport(uid));
            }],
            [i18n('REPORT_CONTENT'), () => {
                dispatch(reportModel.doReportContent('search-members', uid));
            }],
            isOwnerResult || [i18n('IGNORE_USER'), () => {
                dispatch(ignoreModel.doAdd({ uid }));
            }],
            isOwnerResult || [i18n('IGNORE_CONTENT'), () => {
                dispatch(ignoreModel.doAdd({ uid }));
            }],
        ]);
    }, [dispatch, isOwner]);

    const renderItem: ListRenderItem<IGridItem> = useCallback(({ item, index }) => {
        switch (item.type) {
            case 'ad':
                return (
                    <DFApplovinNativeAd
                        placement={NativeAdFrom.MemberSearchResult}
                        containerStyle={{ width: ITEM_WIDTH, }}
                    />
                );

            case 'user': {
                const user = item.data;
                const { uid, avatar, name, gender, age, last_visible_login: timestamp } = user;
                const distance = formatRange(user.distance);
                const isNew = Date.now() - user.signup_date <= 1209600000;
                const live = user.live_location_status === 1;

                return (
                    <>
                        <View style={{ width: ITEM_WIDTH, padding: ITEM_PADDING }}>
                            <GridListCard
                                item={{ uid, avatar, nickname: name, gender, age, distance, timestamp, unread: isNew, live }}
                                canSee={uid !== '0'}
                                onPress={() => onPressItem(user)}
                                onLongPress={() => onLongPress(user)}
                            />
                        </View>
                        {index === 5 && <SecondaryLocationBanner containerStyle={{ width: LIST_WIDTH }} />}
                    </>
                );
            }

            default:
                return null;
        }
    }, [onPressItem, onLongPress]);

    const renderHeader = useCallback(() => {
        return (
            <>
                <SearchInput
                    visibleAnime={animations.searchScale}
                    cancelAnime={animations.cancelBtnWidth}
                    onChange={(nickname) => setSafeState(prevState => ({ ...prevState, nickname }))}
                    onSubmit={byNickname}
                    onCancel={byFilter}
                    value={state.nickname}
                    placeholder={i18n('NICKNAME_SEARCH_PLACEHOLDER')}
                    accessibilityLabel={i18n('A11Y_SEARCH_SEARCHBAR')}
                />
                <RiseUpBanner />
                <AvatarReminderBanner />
                <ShareLocationBanner />
            </>
        );
    }, [animations, setSafeState, state.nickname, byNickname, byFilter]);

    const renderEmpty = useCallback(() => {
        const { params } = state;
        return params.type === 'nickname' ? (
            <EmptyView
                title={i18n('NICKNAME_SEARCH_EMPTY_TITLE')}
                text={i18n('NICKNAME_SEARCH_EMPTY_HINT')}
                link={i18n('CANCEL_NICKNAME_SEARCH')}
                onLinkPress={byFilter}
            />
        ) : (
            <EmptyView
                title={i18n('EMPTY_DISCOVER_LIST_TITLE')}
                text={i18n('EMPTY_DISCOVER_LIST_HINT')}
                link={i18n('GO_TO_SEARCH_FILTER')}
                onLinkPress={() => dispatch(doNavMemberSearchFilter())}
            />
        );
    }, [state, byFilter, dispatch]);

    const renderLoader = useCallback((empty: boolean) => !empty ? null : (
        <View style={{ alignItems: 'center', marginTop: 40 }}>
            {Styles.config.showRadar ? <RadarAni size="60%" /> : <Spinner size="large" />}
        </View>
    ), []);

    // Handle filter changes - only respond to external filter prop changes
    useEffect(() => {
        // Only update if we're in filter mode and the filter actually changed
        if (state.params.type === 'filter') {
            setSafeState(prevState => ({
                ...prevState,
                params: { type: 'filter', data: props.filter }
            }));
        }
    }, [props.filter, setSafeState]); // Removed state.params.type to prevent loops

    return (
        <Screen blank>
            <SearchFilterUpdater
                ref={refUpdater}
                selector={getMemberSearchFilter}
                doFetch={searchModel.doGetDefaultMemberFilter}
                doUpdate={searchModel.doSetMemberSearchFilter}
            />
            <UserIgnoredFilter<IGridItem, number, Params>
                refList={refList}
                filter={(uid: string, item: IGridItem) => item.type === 'user' && item.data.uid === uid}
            />
            <RemoteList<IGridItem, number, Params>
                ref={refList}
                params={state.params}
                preparing={!props.filter.initialized}
                beforeRefresh={() => refUpdater.current?.()}
                keyExtractor={(item) => item.key}
                load={wrappedLoad}
                onScroll={isIOS ? onScroll : undefined}
                renderItem={renderItem}
                renderEmpty={renderEmpty}
                renderHeader={renderHeader}
                renderLoader={renderLoader}
                contentContainerStyle={{ padding: ITEM_PADDING }}
                columnWrapperStyle={{ flexWrap: 'wrap' }}
                numColumns={2}
                refreshable
            />
        </Screen>
    );
};


const ConnectedMemberSearchResultScreen = connect((state) => ({
    filter: getMemberSearchFilter(state),
    uid: getAccountUid(state),
    nativeAdsEntries: getNativeAdsEntries(state),
}))(MemberSearchResultScreen);

const MemberSearchResultScreenWithAds: React.FC<Omit<React.ComponentProps<
    typeof ConnectedMemberSearchResultScreen>, 'adInitialized'>
> = (props) => {
    const isNativeAdsEnabled = useEnableNativeAd(NativeAdFrom.MemberSearchResult);

    return <ConnectedMemberSearchResultScreen {...props} isNativeAdsEnabled={isNativeAdsEnabled} />;
};

export default MemberSearchResultScreenWithAds;
