import type { RewardedAdPlacement } from '@/client/app/applovin/hooks/useShowRewardedAd';
import React, { createContext, useEffect, useMemo, useState } from 'react';
import AppLovinMAX, { ConsentFlowUserGeography } from 'react-native-applovin-max';
import AppInfo from '@/client/config/app';
import WebInfo from '@/client/config/web';
import { logEvent } from '@/client/core/analytics';
import { useSelector } from 'react-redux';
import { needToBannerAds, needToRewardAds } from '@/client/app/status/selectors';
import memoize from 'lodash/memoize';
import useSetupRewardedAd from '@/client/app/applovin/hooks/useSetupRewardedAd';
import { getUnsafeAccountUid } from '@/client/app/auth/selectors';
import { AdBannerPreloader } from '@/client/app/applovin/contexts/AdBannerPreloader';
import { DFApplovinITTProvider } from '@/client/app/applovin/contexts/DFApplovinITTProvider';

export type RewardedAdConfig = {
    daily_rewards_remaining: Record<RewardedAdPlacement, number>;
    settings: { point_per_video: number };
};

const setup = memoize(async (): Promise<boolean> => {
    try {
        AppLovinMAX.setMuted(true);
        AppLovinMAX.setVerboseLogging(__DEV__);
        AppLovinMAX.setConsentFlowDebugUserGeography(ConsentFlowUserGeography.GDPR);
        AppLovinMAX.setTermsAndPrivacyPolicyFlowEnabled(true);
        AppLovinMAX.setPrivacyPolicyUrl(`${AppInfo.WEB_PREFIX}${WebInfo.APP_PRIVACY}`); // mandatory
        AppLovinMAX.setTermsOfServiceUrl(`${AppInfo.WEB_PREFIX}${WebInfo.APP_TERMS}`); // optional
        AppLovinMAX.setTestDeviceAdvertisingIds(['7C98240A-4E21-4EC5-99D4-F6748DF7F131']);

        // eslint-disable-next-line max-len
        const configs = await AppLovinMAX.initialize('V6B3_mKqdc9o_6DGCynQESv7eEZlbfJIs_v8Rs_HlwwkO7tbXqRiFHxWATJPDTVi5uXgIWp1ZbJmLNBirbR9Wb');
        console.log('setup', 'applovin', configs);
    } catch (error) {
        console.error('setup', 'applovin', error);
        logEvent('Applovin_initialize_error');
        return false;
    }

    return true;
});

export type IAdContext = {
    initialized: boolean;
    bannerEnabled: boolean;
    rewardedAdAvailable: boolean;
    config: Nullable<RewardedAdConfig>;
    showRewardedAd: Nullable<(placement: RewardedAdPlacement, uid?: string) => Promise<boolean>>;
};

const initialState: IAdContext = {
    initialized: false,
    bannerEnabled: false,
    rewardedAdAvailable: false,
    config: null,
    showRewardedAd: null,
};

export const DFApplovinAdContext = createContext<IAdContext>(initialState);

export const DFApplovinAdProvider: React.FC<React.PropsWithChildren> = (props) => {
    const showBanner = useSelector(needToBannerAds);
    const needShowRewardAds = useSelector(needToRewardAds);
    const [initialized, setInitialized] = useState(false);
    const { config, available, show: showRewardedAd } = useSetupRewardedAd(initialized);
    const uid = useSelector(getUnsafeAccountUid);

    const providerValue = useMemo(() => ({
        initialized,
        bannerEnabled: showBanner && initialized,
        config,
        rewardedAdAvailable: available,
        showRewardedAd,
    }), [showBanner, config, showRewardedAd, available, initialized]);

    useEffect(() => {
        if ((!showBanner && !needShowRewardAds) || initialized) {
            return;
        }
        setup().then((finished) => {
            if (finished) {
                setInitialized(true);
            }
        });
    }, [showBanner, needShowRewardAds, showRewardedAd, initialized]);

    useEffect(() => {
        uid && initialized && AppLovinMAX.setUserId(uid);
    }, [uid, initialized]);
    return (
        <DFApplovinAdContext.Provider value={providerValue}>
            <AdBannerPreloader maxPreloadedAds={3}>
                <DFApplovinITTProvider>
                    {props.children}
                </DFApplovinITTProvider>
            </AdBannerPreloader>
        </DFApplovinAdContext.Provider>
    );
};

export default DFApplovinAdProvider;
