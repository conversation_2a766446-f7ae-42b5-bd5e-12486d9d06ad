// import { useContext } from 'react';
// import { useSelector } from 'react-redux';
// import { UnitIds } from '@/client/app/applovin/configs';
// import { DFApplovinAdContext } from '@/client/app/applovin/contexts/DFApplovinAdProvider';
import { NativeAdFrom } from '@/client/app/applovin/types/NativeAdFrom';
// import { getNativeAdsEntries, needToNativeAds } from '@/client/app/status/selectors';


export const useEnableNativeAd = (_placement: NativeAdFrom) => {
    // Commented out until logic is implemented to avoid TypeScript warnings
    // const { initialized } = useContext(DFApplovinAdContext);
    // const enabledByRemoteConfig = useSelector(needToNativeAds);
    // const nativeAdsEntries = useSelector(getNativeAdsEntries);
    // const isPlacementAvailable = nativeAdsEntries?.includes(placement);
    // const isIdAvailable = Boolean(UnitIds.native);

    // return enabledByRemoteConfig
    //     && initialized
    //     && isPlacementAvailable
    //     && isIdAvailable;
    return true;
};
