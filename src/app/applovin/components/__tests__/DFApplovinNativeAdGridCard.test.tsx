import React from "react";
import { render } from "@testing-library/react-native";
import { DFApplovinNativeAdGridCard } from "../DFApplovinNativeAdGridCard";

jest.mock("react-native-applovin-max", () => ({
    MediaView: jest.fn(() => <></>),
    TitleView: jest.fn(() => <span>Grid Ad Title</span>),
    BodyView: jest.fn(() => <span>Grid Ad Body</span>),
    CallToActionView: jest.fn(() => <span>Download</span>),
    IconView: jest.fn(() => <></>),
    OptionsView: jest.fn(() => <></>),
}));

jest.mock("@/client/components/DFText", () => {
    const React = require("react");
    return {
        __esModule: true,
        default: ({ children }: any) => <Text>{children}</Text>,
    };
});
import { Text } from "react-native";

jest.mock("@/client/app/applovin/components/CandyGradientBackground", () => ({
    __esModule: true,
    default: jest.fn(() => <></>),
}));

describe("DFApplovinNativeAdGridCard", () => {
    const baseProps = {
        adLoaded: true,
        options: { isOptionsViewAvailable: true },
        mediaViewSize: { width: 120, height: 120 },
        mediaViewContainerSize: { width: 120, height: 120 },
        setMediaViewContainerSize: jest.fn(),
        mediaViewAspectRatio: 1.5,
    };

    it("renders loading state when not loaded", () => {
        const { getByText } = render(
            <DFApplovinNativeAdGridCard {...baseProps} adLoaded={false} />,
        );
        expect(getByText("Loading...")).toBeTruthy();
    });

    it("renders AD badge when loaded", () => {
        const { getByText } = render(
            <DFApplovinNativeAdGridCard {...baseProps} adLoaded={true} />,
        );
        expect(getByText("AD")).toBeTruthy();
    });

    it("calls setMediaViewContainerSize on layout", () => {
        // Not easily testable without a real layout event, but ensure the prop is passed
        expect(typeof baseProps.setMediaViewContainerSize).toBe("function");
    });
});
