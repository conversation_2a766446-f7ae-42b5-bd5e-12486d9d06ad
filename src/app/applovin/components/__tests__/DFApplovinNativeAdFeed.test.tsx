import React from "react";
import { render } from "@testing-library/react-native";
import { DFApplovinNativeAdFeed } from "../DFApplovinNativeAdFeed";

jest.mock("react-native-applovin-max", () => ({
    MediaView: jest.fn(() => <></>),
    TitleView: jest.fn(() => <span>Ad Title</span>),
    BodyView: jest.fn(() => <span>Ad Body</span>),
    CallToActionView: jest.fn(() => <span>Install</span>),
    IconView: jest.fn(() => <></>),
    OptionsView: jest.fn(() => <></>),
}));

jest.mock("@/client/components/DFText", () => {
    const React = require("react");
    return {
        __esModule: true,
        default: ({ children }: any) => <Text>{children}</Text>,
    };
});
import { Text } from "react-native";

jest.mock("@/client/app/applovin/components/CandyGradientBackground", () => ({
    __esModule: true,
    default: jest.fn(() => <></>),
}));

describe("DFApplovinNativeAdFeed", () => {
    const baseProps = {
        adLoaded: true,
        options: { isOptionsViewAvailable: true },
        setMediaViewContainerSize: jest.fn(),
        mediaViewAspectRatio: 1.5,
    };

    it("renders loading state when not loaded", () => {
        const { getByText } = render(
            <DFApplovinNativeAdFeed {...baseProps} adLoaded={false} />,
        );
        expect(getByText("Loading...")).toBeTruthy();
    });

    it("renders AD badge when loaded", () => {
        const { getByText } = render(
            <DFApplovinNativeAdFeed {...baseProps} adLoaded={true} />,
        );
        expect(getByText("AD")).toBeTruthy();
    });

    it("calls setMediaViewContainerSize on layout", () => {
        // Not easily testable without a real layout event, but ensure the prop is passed
        expect(typeof baseProps.setMediaViewContainerSize).toBe("function");
    });
});
