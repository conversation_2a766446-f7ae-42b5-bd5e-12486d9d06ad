import React from "react";
import { render } from "@testing-library/react-native";
import { CandyGradientBackground } from "../CandyGradientBackground";
import { Text } from "react-native";

// Mock DFText as react-native Text
jest.mock("@/client/components/DFText", () => {
    const React = require("react");
    return {
        __esModule: true,
        default: ({ children }: any) => <Text>{children}</Text>,
    };
});

// Mock LinearGradient and BlurView to render children
jest.mock("react-native-linear-gradient", () => ({
    __esModule: true,
    default: ({ children }: any) => <>{children}</>,
}));
jest.mock("@react-native-community/blur", () => ({
    BlurView: ({ children }: any) => <>{children}</>,
}));

describe("CandyGradientBackground", () => {
    it("renders with default props", () => {
        const { toJSON } = render(<CandyGradientBackground />);
        // Accept null or a valid tree (depends on implementation)
        expect(toJSON() === null || typeof toJSON() === "object").toBeTruthy();
    });

    it("applies custom style and opacity", () => {
        const style = { backgroundColor: "pink" };
        const { toJSON } = render(
            <CandyGradientBackground style={style} opacity={0.5} />,
        );
        expect(toJSON() === null || typeof toJSON() === "object").toBeTruthy();
    });

    it("renders with custom blurAmount and blurType", () => {
        const { toJSON } = render(
            <CandyGradientBackground blurAmount={10} blurType="dark" />,
        );
        expect(toJSON() === null || typeof toJSON() === "object").toBeTruthy();
    });

    it("renders children inside the background", () => {
        const { getByText } = render(
            <CandyGradientBackground>
                <React.Fragment>
                    <>Hello World</>
                </React.Fragment>
            </CandyGradientBackground>,
        );
        expect(getByText("Hello World")).toBeTruthy();
    });
});
