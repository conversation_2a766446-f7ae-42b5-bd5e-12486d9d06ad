import {
    getStatusState,
    getLastAccount,
    needToInterstitialAds,
    getInterstitialAdsEntries,
    getInterstitialAdsSpeedDatingSwipeTimes,
} from "../selectors";

describe("status selectors", () => {
    const initialState = {
        status: {
            invisible: false,
            show_invisible_alert: false,
            // ...other status fields
        },
        account: {
            id: "123",
            name: "Test User",
            // ...other account fields
        },
        remoteConfig: {
            interstitial_ads_entries: ["newsfeed", "search"],
            interstitial_ads_speed_dating_swipe_times: 5,
        },
        // ...other root state fields
    };

    it("getStatusState returns status state or initialState", () => {
        expect(getStatusState({ status: initialState.status })).toEqual(
            initialState.status,
        );
        expect(getStatusState(undefined)).toHaveProperty("invisible");
    });

    it("getLastAccount returns last account", () => {
        // This selector may depend on implementation details
        // Placeholder: test with mock state
        expect(typeof getLastAccount).toBe("function");
    });

    it("needToInterstitialAds returns correct value", () => {
        // This selector may depend on account and membership
        // Placeholder: test with mock state
        expect(typeof needToInterstitialAds).toBe("function");
    });

    it("getInterstitialAdsEntries returns entries from remote config", () => {
        const getter = () => initialState.remoteConfig.interstitial_ads_entries;
        expect(getInterstitialAdsEntries.resultFunc(getter)).toEqual([
            "newsfeed",
            "search",
        ]);
    });

    it("getInterstitialAdsSpeedDatingSwipeTimes returns correct value", () => {
        const getter = () =>
            initialState.remoteConfig.interstitial_ads_speed_dating_swipe_times;
        expect(getInterstitialAdsSpeedDatingSwipeTimes.resultFunc(getter)).toBe(
            5,
        );
    });

    // Add more tests for edge cases and other selectors as needed
});
